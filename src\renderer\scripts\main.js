// 检查是否在Electron环境中
const isElectron = typeof window !== 'undefined' && window.process && window.process.type;
const ipcRenderer = isElectron ? require('electron').ipcRenderer : null;

// 应用状态
let appState = {
    originalImages: [],
    selectedImages: [],
    currentPackage: null,
    selectedImageForFrame: null,
    sourceFolderPath: null,

    addonServices: {},
    availablePackages: [],
    frameCategories: [],
    availableFrames: [],

    // 虚拟滚动实例
    originalGalleryVirtualScroll: null,
    selectedGalleryVirtualScroll: null,
    useVirtualScroll: true,  // 启用虚拟滚动优化

    // 回收站功能
    recycledImages: [],  // 存储被删除的图片

    // 精修排序模式: 'filename' | 'selectedAt'
    selectedSortMode: 'filename'
};

// 自动保存定时器
let autosaveTimerId = null;

// DOM 元素
const elements = {
    importBtn: document.getElementById('import-btn'),
    originalGallery: document.getElementById('original-gallery'),
    selectedGallery: document.getElementById('selected-gallery'),
    originalCount: document.getElementById('original-count'),
    selectedCount: document.getElementById('selected-count'),
    packageDescription: document.getElementById('package-description'),
    confirmBtn: document.getElementById('confirm-btn'),
    helpBtn: document.getElementById('help-btn'),
    adminBtn: document.getElementById('admin-btn'),
    selectedImageName: document.getElementById('selected-image-name'),
    galleryTips: document.getElementById('gallery-tips'),
    selectedTips: document.getElementById('selected-tips'),

    loading: document.getElementById('loading'),

    // 新增的元素
    selectPackageBtn: document.getElementById('select-package-btn'),
    changePackageBtn: document.getElementById('change-package-btn'),
    selectedPackageInfo: document.getElementById('selected-package-info'),
    packageModal: document.getElementById('package-modal'),
    packageModalClose: document.querySelector('.package-modal-close'),
    packageOptions: document.getElementById('package-options'),
    packageProductImages: document.getElementById('package-product-images'),
    frameContextMenu: document.getElementById('frame-context-menu'),
    frameMenuOptions: document.getElementById('frame-menu-options'),
    adminPasswordModal: document.getElementById('admin-password-modal'),
    adminPasswordClose: document.querySelector('.admin-password-close'),
    adminPasswordInput: document.getElementById('admin-password-input'),
    adminPasswordCancel: document.getElementById('admin-password-cancel'),
    adminPasswordConfirm: document.getElementById('admin-password-confirm'),

    // 提交成功确认界面元素
    submitSuccessModal: document.getElementById('submit-success-modal'),
    submitSuccessClose: document.querySelector('.submit-success-close'),
    submitSuccessConfirm: document.getElementById('submit-success-confirm'),

    // 回收站相关元素
    recycleBinBtn: document.getElementById('recycle-bin-btn'),
    recycleCount: document.getElementById('recycle-count'),
    recycleBinModal: document.getElementById('recycle-bin-modal'),
    recycleBinClose: document.querySelector('.recycle-bin-close'),
    recycledGallery: document.getElementById('recycled-gallery'),
    emptyRecycleBin: document.getElementById('empty-recycle-bin'),

    // 客户姓名输入相关元素
    customerNameModal: document.getElementById('customer-name-modal'),
    customerNameInput: document.getElementById('customer-name-input'),
    customerPackageInfo: document.getElementById('customer-package-info'),
    customerNameModalClose: document.querySelector('#customer-name-modal .admin-password-close'),
    customerNameCancel: document.getElementById('customer-name-cancel'),
    customerNameConfirm: document.getElementById('customer-name-confirm'),

    // 确认对话框元素
    confirmDialog: document.getElementById('confirm-dialog'),
    confirmTitle: document.getElementById('confirm-title'),
    confirmMessage: document.getElementById('confirm-message'),
    confirmCancel: document.getElementById('confirm-cancel'),
    confirmOk: document.getElementById('confirm-ok'),

    // 冲突处理对话框元素
    conflictDialog: document.getElementById('conflict-dialog'),
    conflictMessage: document.getElementById('conflict-message'),
    conflictImage: document.getElementById('conflict-image'),
    conflictImageName: document.getElementById('conflict-image-name'),
    conflictIgnore: document.getElementById('conflict-ignore'),
    conflictOverride: document.getElementById('conflict-override')
};

// 修复Electron弹出默认对话框后页面失去焦点的bug
// 彻底避免调用系统级对话框：以纯HTML实现 alert/confirm，保证不触发窗口焦点丢失
(function () {
    function createDialogContainer() {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed; inset: 0; z-index: 2147483646;
            display: flex; align-items: center; justify-content: center;
        `;
        const mask = document.createElement('div');
        mask.style.cssText = `position:absolute; inset:0; background: rgba(0,0,0,0.45);`;
        const box = document.createElement('div');
        box.style.cssText = `
            position: relative; z-index: 2; min-width: 360px; max-width: 80vw; background: #fff;
            border-radius: 8px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
        `;
        modal.appendChild(mask);
        modal.appendChild(box);
        return { modal, mask, box };
    }

    function appAlert(message) {
        return new Promise(resolve => {
            const { modal, box } = createDialogContainer();
            box.innerHTML = `
                <div style="padding: 20px 22px;">
                  <div style="font-size: 16px; color: #222; line-height: 1.6; white-space: pre-wrap;">${message || ''}</div>
                  <div style="margin-top: 18px; text-align: right;">
                    <button id="__dlg_ok__" style="padding:8px 16px; background:#1677ff; color:#fff; border:none; border-radius:6px; cursor:pointer;">确定</button>
                  </div>
                </div>`;
            document.body.appendChild(modal);
            const btn = box.querySelector('#__dlg_ok__');
            btn.addEventListener('click', () => {
                modal.remove();
                resolve(true);
            });
            btn.focus();
        });
    }

    function appConfirm(message) {
        return new Promise(resolve => {
            const { modal, box } = createDialogContainer();
            box.innerHTML = `
                <div style="padding: 20px 22px;">
                  <div style="font-size: 16px; color: #222; line-height: 1.6; white-space: pre-wrap;">${message || ''}</div>
                  <div style="margin-top: 18px; text-align: right;">
                    <button id="__dlg_cancel__" style="padding:8px 16px; background:#f0f0f0; color:#333; border:none; border-radius:6px; cursor:pointer; margin-right:8px;">取消</button>
                    <button id="__dlg_ok__" style="padding:8px 16px; background:#1677ff; color:#fff; border:none; border-radius:6px; cursor:pointer;">确认</button>
                  </div>
                </div>`;
            document.body.appendChild(modal);
            const ok = box.querySelector('#__dlg_ok__');
            const cancel = box.querySelector('#__dlg_cancel__');
            ok.addEventListener('click', () => { modal.remove(); resolve(true); });
            cancel.addEventListener('click', () => { modal.remove(); resolve(false); });
            ok.focus();
        });
    }

    // 全局覆盖 window.alert/window.confirm
    window.alert = function (message) {
        return appAlert(message);
    };
    window.confirm = function (message) {
        // 注意：现在是异步返回 Promise<boolean>。原调用方若同步使用，需要改造为 await。
        // 为了兼容旧代码，这里提供一个同步降级：阻塞式不可能，改为立即返回 true，并异步弹框提示。
        // 但我们会在关键路径手动改成 await appConfirm。
        console.warn('confirm() 已被自定义实现覆盖，建议改为: await appConfirm(message)');
        return true;
    };

    // 导出以供显式调用
    window.appAlert = appAlert;
    window.appConfirm = appConfirm;
})();

// 初始化应用
document.addEventListener('DOMContentLoaded', async () => {
    console.log('应用初始化开始');

    // 检查Viewer.js是否加载
    setTimeout(() => {
        if (typeof Viewer !== 'undefined') {
            console.log('Viewer.js 已成功加载');
            // 测试Viewer.js功能
            testViewerSetup();
        } else {
            console.error('Viewer.js 加载失败，将使用简单预览模式');
            console.log('Viewer:', typeof Viewer);
        }
    }, 1000); // Viewer.js加载较快，减少等待时间

    await loadInitialData();
    initializeEventListeners();
    // 启动时尝试恢复上次进度
    await restorePreviousSession();
    updatePackageDisplay(); // 初始化套餐显示状态
    updateUI();
    // 启动10秒自动保存
    startAutoSave();

    console.log('应用初始化完成');
});

// 加载初始数据
async function loadInitialData() {
    try {
        if (ipcRenderer) {
            // Electron环境，从主进程加载数据
            const packages = await ipcRenderer.invoke('get-packages');
            renderPackages(packages);

            const frameCategories = await ipcRenderer.invoke('get-frame-categories');
            appState.frameCategories = frameCategories;

            const frames = await ipcRenderer.invoke('get-frames');
            renderFrames(frames);

            const announcements = await ipcRenderer.invoke('get-announcements');
            renderAnnouncements(announcements);

            const addonServices = await ipcRenderer.invoke('get-addon-services');
            renderAddonServices(addonServices);
        } else {
            // 浏览器环境，使用模拟数据
            const mockData = getMockData();
            renderPackages(mockData.packages);
            appState.frameCategories = mockData.frame_categories || [];
            renderFrames(mockData.frames);
            renderAnnouncements(mockData.announcements);
            renderAddonServices(mockData.addon_services);
        }
    } catch (error) {
        console.error('加载初始数据失败:', error);
    }
}

// 测试Viewer.js设置
function testViewerSetup() {
    try {
        // Viewer.js不需要预定义容器，直接测试构造函数
        console.log('Viewer.js 构造函数可用');
        console.log('Viewer.js 版本:', Viewer.version || 'unknown');
        return true;
    } catch (error) {
        console.error('Viewer.js 测试失败:', error);
        return false;
    }
}

// 获取模拟数据（用于浏览器预览）
function getMockData() {
    return {
        packages: [
            { id: 1, name: '￥1099 套餐', price: 1099, refine_count: 30, description: '基础套餐，包含30张精修照片', image1_path: null, image2_path: null },
            { id: 2, name: '￥1599 套餐', price: 1599, refine_count: 50, description: '标准套餐，包含50张精修照片', image1_path: null, image2_path: null },
            { id: 3, name: '￥2099 套餐', price: 2099, refine_count: 80, description: '豪华套餐，包含80张精修照片', image1_path: null, image2_path: null }
        ],
        frame_categories: [
            { id: 1, name: '简约系列', description: '简洁现代的相框风格', sort_order: 1 },
            { id: 2, name: '复古系列', description: '经典复古的相框风格', sort_order: 2 },
            { id: 3, name: '现代系列', description: '时尚前卫的相框风格', sort_order: 3 },
            { id: 4, name: '自然系列', description: '天然材质的相框风格', sort_order: 4 }
        ],
        frames: [
            { id: 1, name: '简约白框', description: '简洁的白色相框', category_id: 1 },
            { id: 2, name: '复古金框', description: '复古风格的金色相框', category_id: 2 },
            { id: 3, name: '现代黑框', description: '现代简约的黑色相框', category_id: 3 },
            { id: 4, name: '木质相框', description: '天然木质相框', category_id: 4 },
            { id: 5, name: '经典银框', description: '经典银色金属相框', category_id: 1 },
            { id: 6, name: '古典铜框', description: '古典风格铜制相框', category_id: 2 },
            { id: 7, name: '极简边框', description: '极简主义设计', category_id: 3 },
            { id: 8, name: '竹制相框', description: '环保竹制相框', category_id: 4 }
        ],
        announcements: [
            { id: 1, title: '好评活动', content: '五星好评送精修加油包！', type: 'promotion', is_active: true, image_path: null },
            { id: 2, title: '续订福利', content: '老客户续订享受9折优惠', type: 'benefit', is_active: true, image_path: null }
        ],
        addon_services: [
            { id: 1, name: '精修加油包', description: '提供额外的精修照片服务，让您的照片更加完美', type: 'additional', is_active: true, image_path: null },
            { id: 2, name: '入册装帧服务', description: '专业的相册装帧服务，为您的照片提供精美的装帧', type: 'album', is_active: true, image_path: null }
        ]
    };
}

// 渲染套餐选项
function renderPackages(packages) {
    // 存储套餐数据供后续使用
    appState.availablePackages = packages;

    // 渲染模态框中的套餐选项
    const packageOptions = elements.packageOptions;
    packageOptions.innerHTML = '';

    packages.forEach((pkg, index) => {
        const div = document.createElement('div');
        div.className = 'package-option-card';

        // 构建图片HTML
        let imagesHTML = '';
        if (pkg.image1_path || pkg.image2_path) {
            imagesHTML = '<div class="package-images">';
            if (pkg.image1_path) {
                imagesHTML += `<img src="" data-image-path="${pkg.image1_path}" alt="${pkg.name}产品图1" class="package-image">`;
            }
            if (pkg.image2_path) {
                imagesHTML += `<img src="" data-image-path="${pkg.image2_path}" alt="${pkg.name}产品图2" class="package-image">`;
            }
            imagesHTML += '</div>';
        }

        div.innerHTML = `
            <div class="package-card-header">
                <h4>${pkg.name}</h4>
                <div class="package-price">￥${pkg.price}</div>
            </div>
            <div class="package-card-content">
                <p class="package-description">${pkg.description}</p>
                <div class="package-details">
                    <span class="package-count">${pkg.refine_count}张精修</span>
                </div>
                ${imagesHTML}
            </div>
            <button class="select-package-card-btn" data-package-id="${pkg.id}">选择此套餐</button>
        `;
        packageOptions.appendChild(div);
    });

    // 绑定套餐选择事件
    document.querySelectorAll('.select-package-card-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const packageId = parseInt(e.target.dataset.packageId);
            selectPackage(packageId);
        });
    });

    // 加载套餐图片（在套餐选择模态框中）
    loadPackageImages(packageOptions);
}

// 渲染相框选项
function renderFrames(frames) {
    // 存储相框数据供右键菜单使用
    appState.availableFrames = frames;

    // 渲染右键菜单中的相框选项
    const frameMenuOptions = elements.frameMenuOptions;
    if (!frameMenuOptions) {
        console.error('frameMenuOptions 元素未找到');
        return;
    }

    frameMenuOptions.innerHTML = '';
    console.log('开始渲染右键菜单，相框数量:', frames.length);

    // 添加"无相框"选项
    const noFrameOption = document.createElement('div');
    noFrameOption.className = 'context-menu-item';
    noFrameOption.textContent = '无相框';
    noFrameOption.addEventListener('click', () => {
        setImageFrame(null);
        hideFrameContextMenu();
    });
    frameMenuOptions.appendChild(noFrameOption);

    // 按类型分组相框（按照类型的 sort_order 排序组内项）
    const framesByCategory = {};
    const categoryIdToOrder = new Map((appState.frameCategories || []).map(cat => [cat.id, cat.sort_order || 9999]));
    frames.forEach(frame => {
        const category = appState.frameCategories
            ? appState.frameCategories.find(cat => cat.id === frame.category_id)
            : null;
        const categoryName = category ? category.name : '其他';
        if (!framesByCategory[categoryName]) {
            framesByCategory[categoryName] = [];
        }
        framesByCategory[categoryName].push(frame);
    });
    // 组内按相框名称排序，保持一致性（也可按需要改为其它字段）
    Object.keys(framesByCategory).forEach(key => {
        framesByCategory[key].sort((a, b) => a.name.localeCompare(b.name, 'zh-Hans-CN-u-nu-hanidec'));
    });

    console.log('相框分组结果:', framesByCategory);

    // 添加分隔线（在"无相框"后面）
    const separator = document.createElement('div');
    separator.style.borderTop = '1px solid #e9ecef';
    separator.style.margin = '4px 0';
    frameMenuOptions.appendChild(separator);

    // 为每个类型创建二级菜单
    const categoryOrderMap = Object.fromEntries((appState.frameCategories || []).map(cat => [cat.name, cat.sort_order || 9999]));
    const sortedCategories = Object.keys(framesByCategory).sort((a, b) => {
        const sa = (categoryOrderMap[a] !== undefined ? categoryOrderMap[a] : (a === '其他' ? 9999 : 10000));
        const sb = (categoryOrderMap[b] !== undefined ? categoryOrderMap[b] : (b === '其他' ? 9999 : 10000));
        if (sa !== sb) return sa - sb;
        return a.localeCompare(b, 'zh-Hans-CN-u-nu-hanidec');
    });
    sortedCategories.forEach(category => {
        console.log(`创建菜单类别: ${category}, 相框数量: ${framesByCategory[category].length}`);

        const categoryItem = document.createElement('div');
        categoryItem.className = 'context-menu-item has-submenu';

        // 创建文本节点，避免textContent被子元素影响
        const categoryText = document.createElement('span');
        categoryText.textContent = category;
        categoryItem.appendChild(categoryText);

        const submenu = document.createElement('div');
        submenu.className = 'context-submenu';
        submenu.style.display = 'none'; // 确保初始隐藏

        framesByCategory[category].forEach(frame => {
            const frameOption = document.createElement('div');
            frameOption.className = 'context-menu-item';
            frameOption.textContent = frame.name;
            frameOption.title = frame.description; // 添加工具提示
            frameOption.addEventListener('click', () => {
                console.log('选择相框:', frame.name);
                setImageFrame(frame.name);
                hideFrameContextMenu();
            });
            submenu.appendChild(frameOption);
        });

        // 边界检测和位置调整函数
        const adjustSubmenuPosition = () => {
            if (submenu.style.display !== 'block') return;

            // 重置样式，获取默认位置
            submenu.classList.remove('show-left');
            submenu.style.top = '0px';
            submenu.style.transform = '';

            // 强制重新渲染
            submenu.offsetHeight;

            const submenuRect = submenu.getBoundingClientRect();
            const parentRect = categoryItem.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            console.log('边界检测 - 子菜单位置:', {
                submenu: { left: submenuRect.left, right: submenuRect.right, top: submenuRect.top, bottom: submenuRect.bottom },
                viewport: { width: viewportWidth, height: viewportHeight },
                parent: { left: parentRect.left, right: parentRect.right, top: parentRect.top, bottom: parentRect.bottom }
            });

            // 左右边界检测
            let useLeftSide = false;
            if (submenuRect.right > viewportWidth - 10) {
                // 右侧超出，尝试显示在左侧
                useLeftSide = true;
                submenu.classList.add('show-left');

                // 重新获取左侧位置的边界
                const newRect = submenu.getBoundingClientRect();
                if (newRect.left < 10) {
                    // 左侧也超出，选择超出较少的一侧
                    const rightOverflow = submenuRect.right - viewportWidth;
                    const leftOverflow = 10 - newRect.left;

                    if (rightOverflow < leftOverflow) {
                        // 右侧超出较少，还是显示在右侧
                        submenu.classList.remove('show-left');
                        useLeftSide = false;
                    }
                }
            }

            // 上下边界检测
            const currentRect = submenu.getBoundingClientRect();
            let topOffset = 0;

            if (currentRect.bottom > viewportHeight - 10) {
                // 底部超出，往上调整
                topOffset = viewportHeight - currentRect.bottom - 10;
            }

            if (currentRect.top + topOffset < 10) {
                // 调整后顶部超出，重新计算
                topOffset = 10 - currentRect.top;
            }

            if (topOffset !== 0) {
                submenu.style.top = `${topOffset}px`;
            }

            console.log('边界调整结果:', { useLeftSide, topOffset });
        };

        // 显示子菜单的函数
        const showSubmenu = () => {
            console.log('显示子菜单:', category);
            // 隐藏其他子菜单并清理它们的监听器
            const allSubmenus = frameMenuOptions.querySelectorAll('.context-submenu');
            allSubmenus.forEach(sm => {
                if (sm !== submenu) {
                    sm.style.display = 'none';
                    // 清理其他子菜单的监听器
                    if (sm.adjustPosition) {
                        window.removeEventListener('resize', sm.adjustPosition);
                        window.removeEventListener('scroll', sm.adjustPosition, true);
                    }
                }
            });

            // 显示当前子菜单
            submenu.style.display = 'block';

            // 为当前子菜单添加监听器
            window.addEventListener('resize', submenu.adjustPosition);
            window.addEventListener('scroll', submenu.adjustPosition, true);

            // 使用 requestAnimationFrame 确保 DOM 渲染完成后再调整位置
            requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                    adjustSubmenuPosition();
                });
            });
        };

        // 隐藏子菜单的函数
        const hideSubmenu = () => {
            console.log('隐藏子菜单:', category);
            submenu.style.display = 'none';
            // 清理监听器
            if (submenu.adjustPosition) {
                window.removeEventListener('resize', submenu.adjustPosition);
                window.removeEventListener('scroll', submenu.adjustPosition, true);
            }
        };

        // 添加点击事件支持
        categoryItem.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('点击菜单项:', category);

            // 切换子菜单显示状态
            if (submenu.style.display === 'block') {
                hideSubmenu();
            } else {
                showSubmenu();
            }
        });

        // 添加鼠标悬停事件支持
        categoryItem.addEventListener('mouseenter', () => {
            console.log('鼠标进入菜单项:', category);
            showSubmenu();
        });

        // 为子菜单存储调整函数引用，方便后续清理
        submenu.adjustPosition = () => {
            if (submenu.style.display === 'block') {
                adjustSubmenuPosition();
            }
        };

        categoryItem.addEventListener('mouseleave', (e) => {
            const toEl = e.relatedTarget;
            // 若鼠标移向子菜单或仍在当前菜单项，忽略
            if (toEl && (submenu.contains(toEl) || categoryItem.contains(toEl))) {
                return;
            }
            // 检查鼠标是否移动到子菜单上，加入更宽容的延时与命中测试
            setTimeout(() => {
                const el = document.elementFromPoint(e.clientX, e.clientY);
                if (!el || (!submenu.contains(el) && !categoryItem.contains(el))) {
                    hideSubmenu();
                }
            }, 180);
        });

        // 子菜单的鼠标事件
        submenu.addEventListener('mouseleave', (e) => {
            const toEl = e.relatedTarget;
            // 如果移向父菜单项或回到子菜单内部，不隐藏
            if (toEl && (categoryItem.contains(toEl) || submenu.contains(toEl))) {
                return;
            }
            setTimeout(() => {
                const el = document.elementFromPoint(e.clientX, e.clientY);
                if (!el || (!categoryItem.contains(el) && !submenu.contains(el))) {
                    hideSubmenu();
                }
            }, 180);
        });

        categoryItem.appendChild(submenu);
        frameMenuOptions.appendChild(categoryItem);

        console.log(`菜单项 "${category}" 创建完成`);
    });

    console.log('右键菜单渲染完成');
}

// 渲染公告
function renderAnnouncements(announcements) {
    // 分离好评活动和续订福利
    const promotions = announcements.filter(item => item.type === 'promotion' && item.is_active);
    const benefits = announcements.filter(item => item.type === 'benefit' && item.is_active);

    // 渲染好评活动
    const announcementContainer = document.querySelector('#announcements .announcement');
    announcementContainer.innerHTML = '';

    if (promotions.length === 0) {
        const emptyDiv = document.createElement('div');
        emptyDiv.className = 'announcement-item';
        emptyDiv.innerHTML = `
            <h4>暂无活动</h4>
            <p>当前没有进行中的好评活动</p>
        `;
        announcementContainer.appendChild(emptyDiv);
    } else {
        promotions.forEach(announcement => {
            const div = document.createElement('div');
            div.className = 'announcement-item';
            div.innerHTML = `
                <div class="announcement-content">
                    <h4>${announcement.title}</h4>
                    <p>${announcement.content}</p>
                    ${announcement.image_path ? `<div class="announcement-image"><img src="" data-image-path="${announcement.image_path}" alt="${announcement.title}" style="max-width: 100%; height: auto; border-radius: 4px; margin-top: 8px;" onclick="showImageLightbox(this)" title="点击查看大图 - 建议尺寸: 240×120px"></div>` : ''}
                </div>
            `;
            announcementContainer.appendChild(div);
        });

        // 加载图片
        loadAnnouncementImages(announcementContainer);
    }

    // 渲染续订福利
    const renewalContainer = document.querySelector('#renewal-benefits .renewal-benefits');
    renewalContainer.innerHTML = '';

    if (benefits.length === 0) {
        const emptyDiv = document.createElement('div');
        emptyDiv.className = 'announcement-item';
        emptyDiv.innerHTML = `
            <h4>暂无福利</h4>
            <p>当前没有续订福利活动</p>
        `;
        renewalContainer.appendChild(emptyDiv);
    } else {
        benefits.forEach(announcement => {
            const div = document.createElement('div');
            div.className = 'announcement-item';
            div.innerHTML = `
                <div class="announcement-content">
                    <h4>${announcement.title}</h4>
                    <p>${announcement.content}</p>
                    ${announcement.image_path ? `<div class="announcement-image"><img src="" data-image-path="${announcement.image_path}" alt="${announcement.title}" style="max-width: 100%; height: auto; border-radius: 4px; margin-top: 8px;" onclick="showImageLightbox(this)" title="点击查看大图 - 建议尺寸: 240×120px"></div>` : ''}
                </div>
            `;
            renewalContainer.appendChild(div);
        });

        // 加载图片
        loadAnnouncementImages(renewalContainer);
    }
}

// 渲染增值服务
function renderAddonServices(services) {
    const addonContainer = document.querySelector('#addon-services .addon-services');
    addonContainer.innerHTML = '';

    // 过滤出启用的服务
    const activeServices = services.filter(service => service.is_active);

    if (activeServices.length === 0) {
        const emptyDiv = document.createElement('div');
        emptyDiv.className = 'announcement-item';
        emptyDiv.innerHTML = `
            <h4>暂无服务</h4>
            <p>当前没有可用的增加精修入册服务</p>
        `;
        addonContainer.appendChild(emptyDiv);
        return;
    }

    activeServices.forEach(service => {
        const div = document.createElement('div');
        div.className = 'announcement-item';
        div.innerHTML = `
            <div class="announcement-content">
                <h4>${service.name}</h4>
                <p>${service.description}</p>
                ${service.image_path ? `<div class="announcement-image"><img src="" data-image-path="${service.image_path}" alt="${service.name}" style="max-width: 100%; height: auto; border-radius: 4px; margin-top: 8px;" onclick="showImageLightbox(this)" title="点击查看大图 - 建议尺寸: 240×120px"></div>` : ''}
            </div>
        `;
        addonContainer.appendChild(div);
    });

    // 加载图片
    loadAnnouncementImages(addonContainer);
}

// 初始化事件监听器
function initializeEventListeners() {
    // 导入照片按钮
    elements.importBtn.addEventListener('click', importPhotos);

    // 套餐选择相关
    elements.selectPackageBtn.addEventListener('click', showPackageModal);
    elements.changePackageBtn.addEventListener('click', showPackageModal);
    elements.packageModalClose.addEventListener('click', hidePackageModal);

    // 确认提交按钮
    elements.confirmBtn.addEventListener('click', handleConfirmSubmit);

    // 帮助按钮
    elements.helpBtn.addEventListener('click', showHelp);

    // 管理后台按钮
    elements.adminBtn.addEventListener('click', showAdminPasswordModal);

    // 精修排序模式切换
    const selectedSortModeEl = document.getElementById('selected-sort-mode');
    if (selectedSortModeEl) {
        selectedSortModeEl.value = appState.selectedSortMode || 'filename';
        selectedSortModeEl.addEventListener('change', (e) => {
            const mode = e.target.value === 'selectedAt' ? 'selectedAt' : 'filename';
            appState.selectedSortMode = mode;
            renderSelectedGallery();
        });
    }

    // 管理密码验证相关
    elements.adminPasswordClose.addEventListener('click', hideAdminPasswordModal);
    elements.adminPasswordCancel.addEventListener('click', hideAdminPasswordModal);
    elements.adminPasswordConfirm.addEventListener('click', verifyAdminPassword);
    elements.adminPasswordInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            verifyAdminPassword();
        }
    });

    // 客户姓名输入现在使用完全独立的实现，不需要这些事件监听器
    // elements.customerNameModalClose.addEventListener('click', hideCustomerNameModal);
    // elements.customerNameCancel.addEventListener('click', hideCustomerNameModal);
    // elements.customerNameConfirm.addEventListener('click', confirmCustomerName);
    // elements.customerNameInput.addEventListener('keypress', (e) => {
    //     if (e.key === 'Enter') {
    //         confirmCustomerName();
    //     }
    // });

    // 提交成功确认界面相关
    elements.submitSuccessClose.addEventListener('click', hideSubmitSuccessModal);
    elements.submitSuccessConfirm.addEventListener('click', hideSubmitSuccessModal);

    // 折叠功能事件监听器
    initializeCollapsibleHeaders();

    // 键盘事件
    document.addEventListener('keydown', handleKeyDown);

    // 点击套餐模态框背景关闭
    elements.packageModal.addEventListener('click', (e) => {
        if (e.target === elements.packageModal) {
            hidePackageModal();
        }
    });

    // 客户姓名现在使用独立实现，不需要这个事件监听器
    // elements.customerNameModal.addEventListener('click', (e) => {
    //     if (e.target === elements.customerNameModal) {
    //         hideCustomerNameModal();
    //     }
    // });

    // 点击管理密码模态框背景关闭
    elements.adminPasswordModal.addEventListener('click', (e) => {
        if (e.target === elements.adminPasswordModal) {
            hideAdminPasswordModal();
        }
    });

    // 点击提交成功模态框背景关闭
    elements.submitSuccessModal.addEventListener('click', (e) => {
        if (e.target === elements.submitSuccessModal) {
            hideSubmitSuccessModal();
        }
    });

    // 全局点击事件，用于隐藏右键菜单
    document.addEventListener('click', (e) => {
        if (!elements.frameContextMenu.contains(e.target)) {
            hideFrameContextMenu();
        }
    });

    // 回收站相关事件监听器
    elements.recycleBinBtn.addEventListener('click', showRecycleBinModal);
    elements.recycleBinClose.addEventListener('click', hideRecycleBinModal);
    elements.emptyRecycleBin.addEventListener('click', emptyRecycleBin);

    // 确认对话框事件监听器
    elements.confirmCancel.addEventListener('click', hideConfirmDialog);
    elements.confirmOk.addEventListener('click', confirmAction);

    // 冲突处理对话框事件监听器
    elements.conflictIgnore.addEventListener('click', hideConflictDialog);
    elements.conflictOverride.addEventListener('click', handleConflictOverride);

    // 点击回收站模态框背景关闭
    elements.recycleBinModal.addEventListener('click', (e) => {
        if (e.target === elements.recycleBinModal) {
            hideRecycleBinModal();
        }
    });

    // 点击确认对话框背景关闭
    elements.confirmDialog.addEventListener('click', (e) => {
        if (e.target === elements.confirmDialog) {
            hideConfirmDialog();
        }
    });

    // 点击冲突对话框背景关闭
    elements.conflictDialog.addEventListener('click', (e) => {
        if (e.target === elements.conflictDialog) {
            hideConflictDialog();
        }
    });
}

// 初始化折叠头部功能
function initializeCollapsibleHeaders() {
    const collapsibleHeaders = document.querySelectorAll('.collapsible-header');

    collapsibleHeaders.forEach(header => {
        header.addEventListener('click', () => {
            toggleCollapsible(header);
        });
    });
}

// 切换折叠状态
function toggleCollapsible(header) {
    const targetId = header.getAttribute('data-target');
    const content = document.getElementById(targetId);
    const arrow = header.querySelector('.collapse-arrow');

    if (content.classList.contains('collapsed')) {
        // 展开
        content.classList.remove('collapsed');
        header.classList.add('active');
        arrow.textContent = '▼';
    } else {
        // 折叠
        content.classList.add('collapsed');
        header.classList.remove('active');
        arrow.textContent = '▶';
    }
}

// 导入照片
async function importPhotos() {
    // 检查是否已选择套餐
    if (!appState.currentPackage) {
        alert('请先选择套餐再导入照片');
        showPackageModal();
        return;
    }

    if (!ipcRenderer) {
        // 浏览器预览模式，使用模拟数据
        loadMockImages();
        return;
    }

    try {
        showLoading(true);

        const folderPath = await ipcRenderer.invoke('select-folder');
        if (!folderPath) {
            showLoading(false);
            return;
        }

        appState.sourceFolderPath = folderPath;
        const images = await ipcRenderer.invoke('read-images', folderPath);

        appState.originalImages = images.map(img => ({
            name: img.name,
            path: img.path,
            id: generateId()
        }));

        renderOriginalGallery();
        updateUI();
        scheduleAutoSaveNow();

    } catch (error) {
        console.error('导入照片失败:', error);
        alert('导入照片失败，请重试');
    } finally {
        showLoading(false);
    }
}

// 加载模拟图片（用于浏览器预览）
function loadMockImages() {
    // 检查是否已选择套餐
    if (!appState.currentPackage) {
        alert('请先选择套餐再导入照片');
        showPackageModal();
        return;
    }

    showLoading(true);

    try {
        // 模拟大量图片数据以测试虚拟滚动和预览优化
        const mockImages = [];
        const imageCount = 50;  // 50张照片测试虚拟滚动和预览性能

        for (let i = 1; i <= imageCount; i++) {
            mockImages.push({
                name: `IMG_${String(i).padStart(4, '0')}.jpg`,
                path: `https://picsum.photos/400/300?random=${i}`, // 使用Lorem Picsum提供的随机图片
                id: generateId()
            });
        }

        appState.originalImages = mockImages;
        appState.sourceFolderPath = '/mock/photos'; // 模拟路径

        setTimeout(() => {
            renderOriginalGallery();
            updateUI();
            showLoading(false);
            showToast(`已加载 ${mockImages.length} 张模拟照片 ${appState.useVirtualScroll ? '(虚拟滚动模式)' : '(传统模式)'}`, 'info');
            scheduleAutoSaveNow();
        }, 1000); // 模拟加载时间

    } catch (error) {
        console.error('加载模拟图片失败:', error);
        showLoading(false);
        showToast('加载图片失败，请重试', 'error');
    }
}

// 渲染原始照片画廊
function renderOriginalGallery() {
    if (appState.originalImages.length === 0) {
        // 清理虚拟滚动实例
        if (appState.originalGalleryVirtualScroll) {
            appState.originalGalleryVirtualScroll.destroy();
            appState.originalGalleryVirtualScroll = null;
        }

        elements.originalGallery.innerHTML = '<div class="empty-state"><p>点击"导入照片文件夹"开始选片</p></div>';
        elements.originalGallery.classList.remove('virtual-scroll-enabled');
        elements.galleryTips.style.display = 'none';
        return;
    }

    elements.galleryTips.style.display = 'flex';

    // 根据图片数量决定是否使用虚拟滚动
    if (appState.useVirtualScroll && appState.originalImages.length > 10) {
        renderOriginalGalleryWithVirtualScroll();
    } else {
        renderOriginalGalleryTraditional();
    }
}

// 使用虚拟滚动渲染原始照片画廊
function renderOriginalGalleryWithVirtualScroll() {
    console.log('使用虚拟滚动渲染', appState.originalImages.length, '张照片');

    // 清理现有内容
    elements.originalGallery.innerHTML = '';
    elements.originalGallery.classList.add('virtual-scroll-enabled');

    // 销毁现有的虚拟滚动实例
    if (appState.originalGalleryVirtualScroll) {
        appState.originalGalleryVirtualScroll.destroy();
    }

    // 创建虚拟滚动实例
    appState.originalGalleryVirtualScroll = new VirtualScroll(elements.originalGallery, {
        itemHeight: 180,    // 每行高度
        bufferSize: 3,      // 预加载3行
        type: 'original',   // 添加类型标识
        onImageClick: async (image, index, element) => {
            console.log('虚拟滚动图片被点击:', image.name);
            // 选择图片用于相框设置
            selectImageForFrame(image);
            // 显示灯箱
            await showLightbox(image, 'original');
        },
        onImageDoubleClick: (image, index, element) => {
            console.log('图片被双击:', image.name);
            addToSelected(image);
        },
        onImageRightClick: (image, index, element, x, y) => {
            // 原始图片区域不支持右键菜单
            console.log('原始图片右键点击:', image.name);
        }
    });

    // 为每个图片添加状态信息
    const imagesWithState = appState.originalImages.map(image => ({
        ...image,
        isInSelected: appState.selectedImages.some(selected => selected.id === image.id)
    }));

    // 设置数据
    appState.originalGalleryVirtualScroll.setData(imagesWithState);

    // 更新选中状态
    updateOriginalGalleryStates();
}

// 传统方式渲染原始照片画廊（备用方案）
function renderOriginalGalleryTraditional() {
    console.log('使用传统方式渲染', appState.originalImages.length, '张照片');

    elements.originalGallery.classList.remove('virtual-scroll-enabled');
    elements.originalGallery.innerHTML = '';

    appState.originalImages.forEach((image, index) => {
        const thumbnail = createImageThumbnail(image, index, 'original');

        // 检查是否已在精修区
        const isInSelected = appState.selectedImages.some(selected => selected.id === image.id);
        if (isInSelected) {
            thumbnail.classList.add('in-selected');
        }

        elements.originalGallery.appendChild(thumbnail);
    });
}

// 更新原始画廊中的选中状态
function updateOriginalGalleryStates() {
    if (!appState.originalGalleryVirtualScroll) return;

    appState.originalImages.forEach(image => {
        const isInSelected = appState.selectedImages.some(selected => selected.id === image.id);
        appState.originalGalleryVirtualScroll.updateItemState(image.id, {
            inSelected: isInSelected
        });
    });
}

// 渲染选中照片画廊
function renderSelectedGallery() {
    if (appState.selectedImages.length === 0) {
        elements.selectedGallery.innerHTML = '<div class="empty-state"><p>双击底片区的照片添加到精修，或点击预览页面的"选择"按钮</p></div>';
        elements.selectedTips.style.display = 'none';
        return;
    }

    elements.selectedGallery.innerHTML = '';
    elements.selectedTips.style.display = 'flex';

    // 根据排序模式排序后渲染
    const imagesToRender = getSortedSelectedImages();

    imagesToRender.forEach((image, index) => {
        const thumbnail = createImageThumbnail(image, index, 'selected');
        elements.selectedGallery.appendChild(thumbnail);
    });
}

// 获取按当前模式排序后的已选图片
function getSortedSelectedImages() {
    const images = [...appState.selectedImages];
    if (appState.selectedSortMode === 'selectedAt') {
        images.sort((a, b) => {
            const aTime = a.selectedAt ? new Date(a.selectedAt).getTime() : 0;
            const bTime = b.selectedAt ? new Date(b.selectedAt).getTime() : 0;
            return aTime - bTime; // 早选的在前
        });
    } else {
        images.sort((a, b) => (a.name || '').localeCompare(b.name || '', undefined, { numeric: true, sensitivity: 'base' }));
    }
    return images;
}

// 创建图片缩略图
function createImageThumbnail(image, index, type) {
    const div = document.createElement('div');
    div.className = 'image-thumbnail';
    div.dataset.imageId = image.id;
    div.dataset.type = type;

    const img = document.createElement('img');
    // 使用懒加载和占位符
    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjhmOWZhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZjNzU3ZCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWKoOi9veS4rS4uLjwvdGV4dD48L3N2Zz4=';
    img.dataset.src = ipcRenderer ? `file://${image.path}` : image.path;
    img.alt = image.name;
    img.loading = 'lazy';

    // 实现懒加载
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                // 对于模拟图片，直接加载；对于本地文件，使用优化
                if (img.dataset.src.startsWith('https://picsum.photos')) {
                    // 直接加载外部图片
                    img.src = img.dataset.src;
                    img.onload = () => {
                        img.style.opacity = '1';
                    };
                    img.onerror = () => {
                        img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1IiBzdHJva2U9IiNkZGQiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5Yqg6L295aSx6LSlPC90ZXh0Pjwvc3ZnPg==';
                        img.style.opacity = '1';
                    };
                } else {
                    // 对于本地文件使用优化加载
                    loadImageWithOptimization(img, img.dataset.src);
                }
                observer.unobserve(img);
            }
        });
    }, { threshold: 0.1, rootMargin: '50px' }); // 增加rootMargin提前加载

    observer.observe(img);
    img.style.opacity = '0.5';
    img.style.transition = 'opacity 0.3s ease';

    // 去掉文件名显示
    const info = document.createElement('div');
    info.className = 'image-info';
    info.innerHTML = `
        ${image.frame ? `<div class="frame-info">相框: ${image.frame}</div>` : ''}
    `;
    // 如果没有相框信息，完全隐藏info元素
    if (!image.frame) {
        info.style.display = 'none';
    }

    // 添加预览提示图标
    const previewHint = document.createElement('div');
    previewHint.className = 'preview-hint';
    previewHint.innerHTML = '👁️';
    previewHint.title = '点击预览图片';

    div.appendChild(img);
    div.appendChild(info);
    div.appendChild(previewHint);

    // 防抖处理点击事件
    let clickTimeout;
    div.addEventListener('click', (e) => {
        // 阻止事件冒泡，确保点击事件不被其他元素拦截
        e.stopPropagation();
        console.log('图片被点击:', image.name, 'type:', type, 'index:', index);
        clearTimeout(clickTimeout);
        clickTimeout = setTimeout(async () => {
            try {
                if (type === 'selected') {
                    selectImageForFrame(image);
                }
                console.log('准备显示灯箱:', image.name);
                await showLightbox(image, type);
            } catch (error) {
                console.error('显示灯箱时出错:', error);
            }
        }, 200);
    });

    // 双击选择（仅原始区有效）
    div.addEventListener('dblclick', (e) => {
        e.stopPropagation();
        clearTimeout(clickTimeout);
        console.log('图片被双击:', image.name, 'type:', type);
        try {
            if (type === 'original') {
                addToSelected(image);
            } else {
                // 精修区的双击不执行任何操作，避免误删除
                console.log('精修区双击已禁用，请使用预览界面的删除按钮');
            }
        } catch (error) {
            console.error('双击处理出错:', error);
        }
    });

    // 确保缩略图可点击
    div.style.pointerEvents = 'auto';
    div.style.cursor = 'pointer';
    div.style.userSelect = 'none';

    // 右键菜单（仅对精修区的图片）
    if (type === 'selected') {
        div.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            appState.selectedImageForFrame = image;
            showFrameContextMenu(e.clientX, e.clientY);
        });
    }

    return div;
}

// 添加到精修
function addToSelected(image) {
    // 检查是否已经选中
    if (appState.selectedImages.find(img => img.id === image.id)) {
        return;
    }

    // 检查是否在回收站中存在相同图片
    const recycledIndex = appState.recycledImages.findIndex(img => img.id === image.id);
    if (recycledIndex !== -1) {
        // 显示冲突处理对话框
        showConflictDialog(image, recycledIndex);
        return;
    }

    // 取消套餐数量限制
    // const maxCount = getCurrentPackageLimit();
    // if (maxCount && appState.selectedImages.length >= maxCount) {
    //     alert(`当前套餐最多只能选择 ${maxCount} 张照片`);
    //     return;
    // }

    appState.selectedImages.push({
        ...image,
        frame: null,
        selectedAt: new Date().toISOString()
    });
    // 变更后立即触发一次保存节流
    scheduleAutoSaveNow();

    // 如果使用虚拟滚动，更新状态而不是重新渲染
    if (appState.originalGalleryVirtualScroll) {
        // 更新原始画廊中的选中状态
        appState.originalGalleryVirtualScroll.updateItemState(image.id, {
            inSelected: true
        });
        // 重新渲染选中画廊
        renderSelectedGallery();
    } else {
        // 传统方式：重新渲染两个画廊
        renderSelectedGallery();
        renderOriginalGallery();
    }

    updateUI();

    // 显示成功提示
    showToast(`已添加 ${image.name} 到精修区`);
}

// 从精修中移除（移动到回收站）
function removeFromSelected(image) {
    // 从选中列表中移除
    appState.selectedImages = appState.selectedImages.filter(img => img.id !== image.id);

    // 添加到回收站（保存删除时间）
    const recycledImage = {
        ...image,
        deletedAt: new Date().toISOString(),
        deletedFrom: 'selected' // 标记是从精修区删除的
    };
    appState.recycledImages.push(recycledImage);
    scheduleAutoSaveNow();

    // 如果移除的是当前选中的图片，清除相框选择
    if (appState.selectedImageForFrame && appState.selectedImageForFrame.id === image.id) {
        appState.selectedImageForFrame = null;
        updateSelectedImageName(null); // 清除图片名字显示
    }

    // 如果使用虚拟滚动，更新状态而不是重新渲染
    if (appState.originalGalleryVirtualScroll) {
        // 更新原始画廊中的选中状态
        appState.originalGalleryVirtualScroll.updateItemState(image.id, {
            inSelected: false
        });
        // 重新渲染选中画廊
        renderSelectedGallery();
    } else {
        // 传统方式：重新渲染两个画廊
        renderSelectedGallery();
        renderOriginalGallery();
    }

    updateUI();
    updateRecycleBinCount();

    // 显示成功提示
    showToast(`已将 ${image.name} 移至回收站`);
}

// 选择图片用于指定相框（保留用于单击预览）
function selectImageForFrame(image) {
    // 更新图片名字显示
    updateSelectedImageName(image.name);
}

// 这个函数已被新的套餐选择逻辑替代，保留空函数以防其他地方调用
function handlePackageChange(e) {
    // 已被新的套餐选择逻辑替代
}

// 更新增值服务 - 简化为空函数，因为不再有输入框
function updateAddonServices() {
    // 移除增值服务的动态计算，保持为纯展示功能
    appState.addonServices = {};
    updatePriceDisplay();
}

// 更新价格显示
function updatePriceDisplay() {
    // 更新套餐内容显示 - 只显示套餐信息，不再计算增值服务
    if (appState.currentPackage) {
        let description = `${appState.currentPackage.name}<br>包含${appState.currentPackage.count}张精修照片`;
        elements.packageDescription.innerHTML = description;
    }
}

// 这个函数已被右键菜单逻辑替代，保留空函数以防其他地方调用
function handleFrameChange(e) {
    // 已被右键菜单逻辑替代
}

// 处理确认提交
async function handleConfirmSubmit() {
    if (!appState.sourceFolderPath || appState.selectedImages.length === 0) {
        alert('请先导入照片并选择要精修的照片');
        return;
    }

    if (!appState.currentPackage) {
        alert('请选择套餐');
        return;
    }

    try {
        showLoading(true);

        // 准备导出数据（遵循当前排序）
        const sortedForExport = getSortedSelectedImages();
        const selectedImages = sortedForExport.map(img => ({
            originalName: img.name,
            finalName: img.frame ? `${getFileNameWithoutExt(img.name)}_${img.frame}.${getFileExtension(img.name)}` : img.name
        }));

        if (ipcRenderer) {
            // 准备订单信息
            const customerName = appState.currentPackage.customerName || '客户';
            const packagePrice = appState.currentPackage.price;
            const folderName = `${customerName}十${packagePrice}`;

            const orderInfo = {
                package: appState.currentPackage,
                totalImages: appState.selectedImages.length,
                totalPrice: packagePrice,
                customerName: customerName,
                folderName: folderName  // 精修文件夹名称
            };

            // Electron环境
            const result = await ipcRenderer.invoke('export-selected-images', {
                sourceFolderPath: appState.sourceFolderPath,
                selectedImages,
                orderInfo
            });

            if (result.success) {
                showSubmitSuccessModal();
                // 导出成功后清除会话
                try { await ipcRenderer.invoke('clear-session'); } catch(e) { console.warn('清除会话失败:', e); }
            } else {
                alert(`导出失败：${result.error}`);
            }
        } else {
            // 浏览器环境，显示选择结果
            showSubmitSuccessModal();
        }

    } catch (error) {
        console.error('导出失败:', error);
        alert('导出失败，请重试');
    } finally {
        showLoading(false);
    }
}

// 优化图片加载 - 处理大图片
function loadImageWithOptimization(imgElement, src) {
    const img = new Image();

    // 设置跨域属性以支持外部图片
    img.crossOrigin = 'anonymous';

    img.onload = () => {
        try {
            // 检查是否是本地文件或需要优化的大图片
            if (src.startsWith('file://') || src.startsWith('data:')) {
                // 对于本地文件，进行Canvas优化
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                    // 计算缩略图尺寸（最大400px，与底片区保持一致）
    const maxSize = 400;
    let { width, height } = img;

    if (width > height) {
        if (width > maxSize) {
            height = (height * maxSize) / width;
            width = maxSize;
        }
    } else {
        if (height > maxSize) {
            width = (width * maxSize) / height;
            height = maxSize;
        }
    }

    canvas.width = width;
    canvas.height = height;

    // 绘制缩略图
    ctx.drawImage(img, 0, 0, width, height);

    // 使用优化后的图片（提高质量到0.85，与底片区保持一致）
    imgElement.src = canvas.toDataURL('image/jpeg', 0.85);
                imgElement.style.opacity = '1';

                // 清理canvas
                canvas.remove();
            } else {
                // 对于外部URL（如picsum.photos），直接使用原图
                imgElement.src = src;
                imgElement.style.opacity = '1';
            }
        } catch (error) {
            console.warn('图片优化失败，使用原图:', error);
            // 如果Canvas处理失败，直接使用原图
            imgElement.src = src;
            imgElement.style.opacity = '1';
        }
    };

    img.onerror = () => {
        console.warn('图片加载失败:', src);
        // 如果加载失败，显示错误占位符
        imgElement.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1IiBzdHJva2U9IiNkZGQiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5Yqg6L295aSx6LSlPC90ZXh0Pjwvc3ZnPg==';
        imgElement.style.opacity = '1';
    };

    img.src = src;
}

// Viewer.js 自动处理图片尺寸，不需要手动获取

// 添加自定义"加入精修"按钮到 Viewer.js 工具栏
function addCustomRefineButton(viewer, images) {
    try {
        // 查找工具栏
        const toolbar = document.querySelector('.viewer-toolbar');
        if (!toolbar) {
            console.warn('未找到 Viewer.js 工具栏');
            return;
        }

        // 创建自定义按钮 - 使用标准的 viewer 按钮结构
        const refineButton = document.createElement('li');
        refineButton.className = 'viewer-button viewer-button-refine';
        refineButton.setAttribute('role', 'button');
        refineButton.setAttribute('tabindex', '0');
        refineButton.title = '加入精修 (快捷键: E)';

        // 创建按钮内容 - 使用SVG图标让它更美观
        refineButton.innerHTML = `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </svg>
        `;

        // 添加点击事件
        refineButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            const currentIndex = viewer.index;
            const currentImage = images[currentIndex];
            if (currentImage) {
                addToSelected(currentImage);
                showToast(`已将 ${currentImage.name} 加入精修`);
                // 添加视觉反馈
                refineButton.style.background = '#28a745';
                setTimeout(() => {
                    refineButton.style.background = '';
                }, 300);
            }
        });

        // 将按钮插入到工具栏的合适位置（在播放按钮之后）
        const playButton = toolbar.querySelector('[data-viewer-action="play"]');
        if (playButton && playButton.parentNode) {
            playButton.parentNode.insertBefore(refineButton, playButton.nextSibling);
        } else {
            // 如果找不到播放按钮，就添加到工具栏末尾
            toolbar.appendChild(refineButton);
        }

        console.log('自定义"加入精修"按钮已添加到工具栏');
    } catch (error) {
        console.error('添加自定义按钮失败:', error);
    }
}

// 动态图片查看器类 - (V4 - 占位符 + 按需加载最终版)
async function showLightbox(image, type) {
    console.log(`[V4 Init] showLightbox for image: ${image.name}`);

    // 检查Viewer.js是否已加载
    if (typeof Viewer === 'undefined') {
        console.error('Viewer.js 未加载，使用简单预览模式');
        showSimpleImagePreview(image, type);
        return;
    }

    const images = type === 'original' ? appState.originalImages : getSortedSelectedImages();
    const currentIndex = images.findIndex(img => img.id === image.id);
    let viewer = null;

    // 1. 创建一个容器，并为所有图片创建"影子"DOM元素
    const container = document.createElement('div');
    container.style.display = 'none';

    for (const imgData of images) {
        const imgElement = document.createElement('img');
        imgElement.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
        imgElement.dataset.src = ipcRenderer ? `file://${imgData.path}` : imgData.path;
        imgElement.alt = imgData.name + (imgData.frame ? ` (相框: ${imgData.frame})` : '');
        container.appendChild(imgElement);
    }
    document.body.appendChild(container);

    // 2. 初始化Viewer.js
    viewer = new Viewer(container, {
        initialViewIndex: currentIndex,
        inline: false,
        loading: true,
        transition: false, // 关闭弹出过渡效果，使用平滑过渡
        toolbar: true, // 启用工具栏

        url(image) {
            const highResSrc = image.dataset.src;
            console.log(`[Viewer URL] Loading high-res image: ${highResSrc}`);
            return createOptimizedImageUrl(highResSrc, 1600);
        },

        viewed(event) {
            const viewedIndex = event.detail.index;
            const loadThumbnail = (index) => {
                if (index < 0 || index >= viewer.images.length) return;
                const imgElement = viewer.images[index];
                if (imgElement.src.startsWith('data:image/gif')) {
                    imgElement.src = createOptimizedImageUrl(imgElement.dataset.src, 200);
                }
            };
            loadThumbnail(viewedIndex);
            loadThumbnail(viewedIndex + 1);
            loadThumbnail(viewedIndex - 1);
        },

        shown() {
            // 在Viewer完全显示后，安全地添加自定义按钮
            console.log('🔍 Viewer shown 事件触发，开始查找工具栏...');

            // 等待一下确保DOM完全渲染
            setTimeout(() => {
                try {
                    // 尝试多种选择器找到工具栏
                    let toolbar = document.querySelector('.viewer-toolbar');
                    console.log('🔍 找到 .viewer-toolbar:', !!toolbar);

                    if (toolbar) {
                        console.log('🔍 工具栏HTML:', toolbar.outerHTML.substring(0, 200));

                        // 尝试找到工具栏内的按钮容器
                        let buttonContainer = toolbar.querySelector('ul') || toolbar;
                        console.log('🔍 找到按钮容器:', !!buttonContainer);

                        // 清除之前添加的自定义按钮，避免重复
                        const existingRefineBtn = buttonContainer.querySelector('.viewer-refine-btn');
                        const existingDeleteBtn = buttonContainer.querySelector('.viewer-delete-btn');
                        if (existingRefineBtn) existingRefineBtn.remove();
                        if (existingDeleteBtn) existingDeleteBtn.remove();

                        // 判断当前是底片区还是精修区
                        const isRefinedSection = type !== 'original';

                        if (!isRefinedSection) {
                            // 底片区：创建橙色的"选择"按钮
                            const refineButton = document.createElement('li');
                            refineButton.setAttribute('tabindex', '0');
                            refineButton.setAttribute('role', 'button');
                            refineButton.className = 'viewer-refine-btn';
                            refineButton.setAttribute('data-viewer-action', 'refine');
                            refineButton.title = '点击加入精修 (快捷键: E)';
                            refineButton.innerHTML = '选择';

                            refineButton.addEventListener('click', (e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                console.log('🎯 精修按钮被点击！');
                                const currentImage = images[viewer.index];
                                if (currentImage) {
                                    addToSelected(currentImage);
                                    showToast(`已将 ${currentImage.name} 加入精修`);
                                }
                            });

                            // 直接添加到工具栏
                            buttonContainer.appendChild(refineButton);
                            console.log('✅ 选择按钮已添加到工具栏');
                        } else {
                            // 精修区：创建红色的"删除"按钮
                            const deleteButton = document.createElement('li');
                            deleteButton.setAttribute('tabindex', '0');
                            deleteButton.setAttribute('role', 'button');
                            deleteButton.className = 'viewer-delete-btn';
                            deleteButton.setAttribute('data-viewer-action', 'delete');
                            deleteButton.title = '从精修区删除 (快捷键: D)';
                            deleteButton.innerHTML = '删除';

                            deleteButton.addEventListener('click', (e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                console.log('🎯 删除按钮被点击！');
                                const currentImage = images[viewer.index];
                                if (currentImage) {
                                    const currentIndex = viewer.index;

                                    // 从精修区移除图片
                                    removeFromSelected(currentImage);

                                    // 如果删除后精修区为空，关闭预览
                                    if (appState.selectedImages.length === 0) {
                                        viewer.hide();
                                        return;
                                    }

                                    // 获取删除后按当前排序模式排序的图片数组
                                    const sortedAfterDeletion = getSortedSelectedImages();

                                    // 计算新的索引位置（按排序后的数组）
                                    let newIndex = currentIndex;
                                    // 如果删除的是最后一张，跳到前一张
                                    if (newIndex >= sortedAfterDeletion.length) {
                                        newIndex = sortedAfterDeletion.length - 1;
                                    }

                                    // 关闭当前viewer并重新打开，显示剩余图片
                                    viewer.hide();

                                    // 延迟一下再重新打开，确保viewer完全关闭
                                    setTimeout(() => {
                                        if (sortedAfterDeletion.length > 0 && sortedAfterDeletion[newIndex]) {
                                            console.log(`🔄 删除后跳转到第${newIndex + 1}张图片: ${sortedAfterDeletion[newIndex].name} (排序模式: ${appState.selectedSortMode})`);
                                            showLightbox(sortedAfterDeletion[newIndex], 'selected');
                                        }
                                    }, 100);
                                }
                            });

                            // 直接添加到工具栏
                            buttonContainer.appendChild(deleteButton);
                            console.log('✅ 删除按钮已添加到工具栏');
                        }

                    } else {
                        console.error('❌ 未找到 Viewer.js 工具栏');
                        console.log('🔍 当前页面所有可能的工具栏元素:');
                        document.querySelectorAll('[class*="toolbar"], [class*="tool"]').forEach((el, i) => {
                            console.log(`  ${i}: ${el.className} - ${el.tagName}`);
                        });
                    }
                } catch (error) {
                    console.error('❌ 添加自定义按钮失败:', error);
                }
            }, 200);
        },

        hidden() {
            viewer.destroy();
            if (container.parentNode) {
                container.parentNode.removeChild(container);
            }
        }
    });

    console.log(`[V4 Start] Viewer initialized with ${images.length} placeholders.`);
    viewer.show();
}

// 简单的图片预览（降级方案）
function showSimpleImagePreview(image, type) {
    console.log('使用简单图片预览:', image.name);

    const images = type === 'original' ? appState.originalImages : getSortedSelectedImages();
    let currentIndex = images.findIndex(img => img.id === image.id);

    // 创建简单的模态框
    const modal = document.createElement('div');
    modal.className = 'simple-lightbox';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        user-select: none;
    `;

    // 图片容器
    const imgContainer = document.createElement('div');
    imgContainer.style.cssText = `
        position: relative;
        max-width: 90%;
        max-height: 90%;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    const img = document.createElement('img');
    img.style.cssText = `
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        transition: opacity 0.25s ease-in-out, transform 0.3s ease;
        cursor: grab;
        opacity: 0;
    `;

    // 缩放和拖拽状态
    let scale = 1;
    let isDragging = false;
    let startX = 0;
    let startY = 0;
    let translateX = 0;
    let translateY = 0;

    // 关闭按钮
    const closeBtn = document.createElement('div');
    closeBtn.innerHTML = '×';
    closeBtn.style.cssText = `
        position: absolute;
        top: 20px;
        right: 30px;
        color: white;
        font-size: 40px;
        cursor: pointer;
        z-index: 10001;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.5);
        transition: background 0.3s ease;
    `;

    // 左右箭头
    const prevBtn = document.createElement('div');
    prevBtn.innerHTML = '‹';
    prevBtn.style.cssText = `
        position: absolute;
        left: 20px;
        top: 50%;
        transform: translateY(-50%);
        color: white;
        font-size: 60px;
        cursor: pointer;
        z-index: 10001;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.5);
        transition: background 0.3s ease;
        ${images.length <= 1 ? 'display: none;' : ''}
    `;

    const nextBtn = document.createElement('div');
    nextBtn.innerHTML = '›';
    nextBtn.style.cssText = `
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        color: white;
        font-size: 60px;
        cursor: pointer;
        z-index: 10001;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.5);
        transition: background 0.3s ease;
        ${images.length <= 1 ? 'display: none;' : ''}
    `;

    // 图片信息
    const infoBar = document.createElement('div');
    infoBar.style.cssText = `
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        color: white;
        background: rgba(0, 0, 0, 0.7);
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 14px;
        z-index: 10001;
    `;

    imgContainer.appendChild(img);
    modal.appendChild(imgContainer);
    modal.appendChild(closeBtn);
    modal.appendChild(prevBtn);
    modal.appendChild(nextBtn);
    modal.appendChild(infoBar);
    document.body.appendChild(modal);

    // 更新图片变换
    function updateImageTransform() {
        img.style.transform = `translate(${translateX}px, ${translateY}px) scale(${scale})`;
        img.style.cursor = scale > 1 ? (isDragging ? 'grabbing' : 'grab') : 'grab';
    }

    // 重置图片状态
    function resetImageState() {
        scale = 1;
        translateX = 0;
        translateY = 0;
        updateImageTransform();
        showZoomLevel(scale);
    }

    // 显示缩放级别提示
    function showZoomLevel(currentScale) {
        // 移除现有的缩放提示
        const existingTip = modal.querySelector('.zoom-level-tip');
        if (existingTip) {
            existingTip.remove();
        }

        // 创建缩放级别提示
        const zoomTip = document.createElement('div');
        zoomTip.className = 'zoom-level-tip';
        zoomTip.textContent = `${Math.round(currentScale * 100)}%`;
        zoomTip.style.cssText = `
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            z-index: 10003;
            pointer-events: none;
            opacity: 1;
            transition: opacity 0.3s ease;
        `;

        modal.appendChild(zoomTip);

        // 自动隐藏提示
        setTimeout(() => {
            if (zoomTip.parentNode) {
                zoomTip.style.opacity = '0';
                setTimeout(() => {
                    if (zoomTip.parentNode) {
                        zoomTip.remove();
                    }
                }, 300);
            }
        }, 1500);
    }

    // 图片预加载缓存
    const imageCache = new Map();

    // 预加载图片函数
    const preloadImageForSimpleViewer = async (index) => {
        if (index < 0 || index >= images.length || imageCache.has(index)) {
            return;
        }

        const targetImg = images[index];
        const imageSrc = ipcRenderer ? `file://${targetImg.path}` : targetImg.path;
        const optimizedSrc = createOptimizedImageUrl(imageSrc);

        try {
            const preloadImg = new Image();
            preloadImg.crossOrigin = 'anonymous';

            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('预加载超时'));
                }, 8000);

                preloadImg.onload = () => {
                    clearTimeout(timeout);
                    resolve();
                };
                preloadImg.onerror = (error) => {
                    clearTimeout(timeout);
                    reject(error);
                };
                preloadImg.src = optimizedSrc;
            });

            // 缓存成功加载的图片
            imageCache.set(index, preloadImg.src);
            console.log(`预加载图片 ${index + 1} 成功`);

        } catch (error) {
            console.warn(`预加载图片 ${index + 1} 失败:`, error);
        }
    };

    // 预加载相邻图片
    const preloadAdjacentImages = (centerIndex) => {
        // 异步预加载前后图片，不阻塞界面
        setTimeout(() => {
            const preloadTasks = [];
            if (centerIndex > 0) {
                preloadTasks.push(preloadImageForSimpleViewer(centerIndex - 1));
            }
            if (centerIndex < images.length - 1) {
                preloadTasks.push(preloadImageForSimpleViewer(centerIndex + 1));
            }

            Promise.all(preloadTasks).catch(error => {
                console.warn('预加载相邻图片失败:', error);
            });
        }, 200);
    };

    // 更新图片显示 - Windows照片查看器风格的平滑过渡
    async function updateImage() {
        const currentImg = images[currentIndex];
        infoBar.textContent = `${currentImg.name} (${currentIndex + 1}/${images.length})`;

        if (currentImg.frame) {
            infoBar.textContent += ` - 相框: ${currentImg.frame}`;
        }

        // 先淡出当前图片，然后加载新图片
        img.style.transition = 'opacity 0.15s ease-in-out';
        img.style.opacity = '0';

        try {
            // 检查缓存
            if (imageCache.has(currentIndex)) {
                setTimeout(() => {
                    img.src = imageCache.get(currentIndex);
                    img.style.transition = 'opacity 0.25s ease-in-out';
                    img.style.opacity = '1';
                    console.log(`使用缓存图片 ${currentIndex + 1}`);
                }, 150);

                // 预加载相邻图片
                preloadAdjacentImages(currentIndex);
                return;
            }

            // 异步加载图片
            const imageSrc = ipcRenderer ? `file://${currentImg.path}` : currentImg.path;
            const optimizedSrc = createOptimizedImageUrl(imageSrc);

            // 预加载图片
            const preloadImg = new Image();
            preloadImg.crossOrigin = 'anonymous';

            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('图片加载超时'));
                }, 10000);

                preloadImg.onload = () => {
                    clearTimeout(timeout);
                    resolve();
                };
                preloadImg.onerror = (error) => {
                    clearTimeout(timeout);
                    reject(error);
                };
                preloadImg.src = optimizedSrc;
            });

            // 等待淡出完成，然后淡入新图片
            setTimeout(() => {
                img.src = preloadImg.src;
                // 使用稍微长一点的过渡时间来淡入新图片
                img.style.transition = 'opacity 0.25s ease-in-out';
                img.style.opacity = '1';
            }, 150); // 等待淡出动画完成

            // 缓存成功加载的图片
            imageCache.set(currentIndex, preloadImg.src);

            // 预加载相邻图片
            preloadAdjacentImages(currentIndex);

        } catch (error) {
            console.warn('简单预览图片加载失败:', error);
            // 错误情况也使用相同的平滑过渡
            setTimeout(() => {
                img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1IiBzdHJva2U9IiNkZGQiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5Yqg6L295aSx6LSlPC90ZXh0Pjwvc3ZnPg==';
                img.style.transition = 'opacity 0.25s ease-in-out';
                img.style.opacity = '1';
            }, 150);
        }

        // 重置缩放状态
        resetImageState();
    }

    // 初始化显示
    updateImage();

    // 关闭事件
    const closeModal = () => {
        if (modal.parentNode) {
            document.body.removeChild(modal);
        }
        // 清理所有事件监听器
        document.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
    };

    // 上一张
    const showPrev = async () => {
        if (currentIndex > 0) {
            currentIndex--;
            await updateImage();
        }
    };

    // 下一张
    const showNext = async () => {
        if (currentIndex < images.length - 1) {
            currentIndex++;
            await updateImage();
        }
    };

    // 事件监听
    closeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        closeModal();
    });

    prevBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        showPrev();
    });

    nextBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        showNext();
    });

    // 点击背景关闭
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal();
        }
    });

    // 鼠标滚轮缩放
    const handleWheel = (e) => {
        e.preventDefault();
        e.stopPropagation();

        // 缩放步长调整 - 更精细的缩放控制
        const delta = e.deltaY > 0 ? -0.15 : 0.15;
        const newScale = Math.max(0.3, Math.min(5, scale + delta));

        if (newScale !== scale) {
            // 获取鼠标相对于图片中心的位置
            const rect = img.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            // 计算鼠标相对于图片中心的偏移
            const mouseX = e.clientX - centerX;
            const mouseY = e.clientY - centerY;

            // 计算新的平移量，实现以鼠标位置为中心的缩放
            const scaleRatio = newScale / scale;
            translateX = translateX * scaleRatio + mouseX * (1 - scaleRatio);
            translateY = translateY * scaleRatio + mouseY * (1 - scaleRatio);

            scale = newScale;
            updateImageTransform();

            // 显示缩放级别提示
            showZoomLevel(scale);
        }
    };

    // 鼠标拖拽
    const handleMouseDown = (e) => {
        if (scale > 1) {
            isDragging = true;
            startX = e.clientX - translateX;
            startY = e.clientY - translateY;
            img.style.cursor = 'grabbing';
            e.preventDefault();
        }
    };

    const handleMouseMove = (e) => {
        if (isDragging && scale > 1) {
            translateX = e.clientX - startX;
            translateY = e.clientY - startY;
            updateImageTransform();
        }
    };

    const handleMouseUp = () => {
        isDragging = false;
        img.style.cursor = scale > 1 ? 'grab' : 'grab';
    };

    // 双击重置缩放
    const handleDoubleClick = (e) => {
        e.stopPropagation();
        if (scale > 1.1) {
            // 如果放大了，重置到适合屏幕
            resetImageState();
        } else {
            // 如果是原始大小，放大到2倍
            scale = 2;
            // 以双击位置为中心放大
            const rect = img.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            const mouseX = e.clientX - centerX;
            const mouseY = e.clientY - centerY;

            translateX = -mouseX;
            translateY = -mouseY;
            updateImageTransform();
            showZoomLevel(scale);
        }
    };

    // 绑定图片事件
    img.addEventListener('wheel', handleWheel);
    img.addEventListener('mousedown', handleMouseDown);
    img.addEventListener('dblclick', handleDoubleClick);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // 键盘事件
    const handleKeyDown = (e) => {
        switch (e.key) {
            case 'Escape':
                closeModal();
                break;
            case 'ArrowLeft':
                e.preventDefault();
                showPrev();
                break;
            case 'ArrowRight':
                e.preventDefault();
                showNext();
                break;
            case 'e': // 精修快捷键
            case 'E':
                e.preventDefault();
                const currentImage = images[currentIndex];
                if (currentImage) {
                    addToSelected(currentImage);
                    showToast(`已将 ${currentImage.name} 加入精修`);
                }
                break;
            case 'd': // 删除快捷键（仅在精修区有效）
            case 'D':
                e.preventDefault();
                // 检查当前是否在Viewer.js预览模式中
                const viewerContainer = document.querySelector('.viewer-container');
                if (viewerContainer && viewerContainer.style.display !== 'none') {
                    // 在Viewer预览中，通过检查当前是否在精修区来决定是否删除
                    const deleteBtn = document.querySelector('.viewer-delete-btn');
                    if (deleteBtn) {
                        deleteBtn.click(); // 触发删除按钮的点击事件
                    }
                }
                break;
            case '=':
            case '+':
                e.preventDefault();
                scale = Math.min(5, scale + 0.25);
                updateImageTransform();
                showZoomLevel(scale);
                break;
            case '-':
                e.preventDefault();
                scale = Math.max(0.3, scale - 0.25);
                updateImageTransform();
                showZoomLevel(scale);
                break;
            case '0':
                e.preventDefault();
                resetImageState();
                break;
            case '1':
                e.preventDefault();
                scale = 1;
                translateX = 0;
                translateY = 0;
                updateImageTransform();
                showZoomLevel(scale);
                break;
            case '2':
                e.preventDefault();
                scale = 2;
                updateImageTransform();
                showZoomLevel(scale);
                break;
        }
    };
    document.addEventListener('keydown', handleKeyDown);

    // 鼠标悬停效果
    closeBtn.addEventListener('mouseenter', () => {
        closeBtn.style.background = 'rgba(255, 255, 255, 0.2)';
    });
    closeBtn.addEventListener('mouseleave', () => {
        closeBtn.style.background = 'rgba(0, 0, 0, 0.5)';
    });

    prevBtn.addEventListener('mouseenter', () => {
        prevBtn.style.background = 'rgba(255, 255, 255, 0.2)';
    });
    prevBtn.addEventListener('mouseleave', () => {
        prevBtn.style.background = 'rgba(0, 0, 0, 0.5)';
    });

    nextBtn.addEventListener('mouseenter', () => {
        nextBtn.style.background = 'rgba(255, 255, 255, 0.2)';
    });
    nextBtn.addEventListener('mouseleave', () => {
        nextBtn.style.background = 'rgba(0, 0, 0, 0.5)';
    });
}



// 处理键盘事件
function handleKeyDown(e) {
    // 如果独立的客户信息表单弹窗存在，则不拦截除 ESC 外的键盘事件
    const independentModal = document.getElementById('independent-customer-modal');
    const activeEl = document.activeElement;
    const tag = activeEl && activeEl.tagName ? activeEl.tagName.toLowerCase() : '';
    const isTypingElement = tag === 'input' || tag === 'textarea' || (activeEl && activeEl.isContentEditable);

    if (independentModal) {
        if (e.key === 'Escape') {
            // 关闭独立弹窗（使用安全关闭，恢复指针事件）
            if (typeof window.safelyCloseIndependentCustomerModal === 'function') {
                window.safelyCloseIndependentCustomerModal();
            } else {
                independentModal.remove();
            }
        }
        // 让输入框接管键盘事件
        return;
    }

    // 若旧版客户姓名输入弹窗打开，则仅允许 ESC 关闭，不拦截其它按键
    if (elements.customerNameModal && elements.customerNameModal.style.display === 'block') {
        if (e.key === 'Escape') {
            hideCustomerNameModal();
        }
        return;
    }

    // 如果当前焦点在可输入元素上，则不触发全局快捷键
    if (isTypingElement) {
        return;
    }

    // ESC键关闭模态框
    if (e.key === 'Escape') {
        if (elements.submitSuccessModal.style.display === 'flex') {
            hideSubmitSuccessModal();
            return;
        }

        if (elements.packageModal.style.display === 'block') {
            hidePackageModal();
            return;
        }
        if (elements.adminPasswordModal.style.display === 'block') {
            hideAdminPasswordModal();
            return;
        }
    }

    // 全局快捷键
    if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
            case 'i':
                e.preventDefault();
                importPhotos();
                break;
            case 'Enter':
                e.preventDefault();
                if (!elements.confirmBtn.disabled) {
                    handleConfirmSubmit();
                }
                break;
        }
    }

    // 数字键选择套餐（如果套餐模态框打开）
    if (e.key >= '1' && e.key <= '9' && elements.packageModal.style.display === 'block') {
        const index = parseInt(e.key) - 1;
        if (appState.availablePackages[index]) {
            selectPackage(appState.availablePackages[index].id);
        }
    }
}

// 更新UI状态
function updateUI() {
    // 更新计数器
    elements.originalCount.textContent = `(${appState.originalImages.length})`;

    // 去掉数量限制显示，只显示当前选中的数量
    const selectedCount = appState.selectedImages.length;
    elements.selectedCount.textContent = `(${selectedCount})`;
    elements.selectedCount.style.color = selectedCount > 0 ? '#28a745' : '#333';

    // 更新回收站计数
    updateRecycleBinCount();

    // 更新确认按钮状态 - 去掉数量限制
    const canConfirm = appState.currentPackage &&
                      appState.selectedImages.length > 0 &&
                      appState.sourceFolderPath;

    elements.confirmBtn.disabled = !canConfirm;

    // 更新按钮文本提示 - 去掉数量限制检查
    if (!appState.currentPackage) {
        elements.confirmBtn.textContent = '请先选择套餐';
    } else if (appState.selectedImages.length === 0) {
        elements.confirmBtn.textContent = '请选择要精修的照片';
    } else if (!appState.sourceFolderPath) {
        elements.confirmBtn.textContent = '请先导入照片';
    } else {
        elements.confirmBtn.textContent = '我选好了，确认提交';
    }
}

// 获取当前套餐限制
function getCurrentPackageLimit() {
    return appState.currentPackage ? appState.currentPackage.count : 0;
}

// 显示/隐藏加载状态
function showLoading(show) {
    elements.loading.style.display = show ? 'flex' : 'none';
}

// 工具函数
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}

function getFileNameWithoutExt(filename) {
    return filename.substring(0, filename.lastIndexOf('.'));
}

function getFileExtension(filename) {
    return filename.substring(filename.lastIndexOf('.') + 1);
}

// 显示toast提示
function showToast(message, type = 'success') {
    // 移除现有的toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }

    // 创建新的toast
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    // 添加到页面
    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}



// 截断文件名
function truncateFileName(fileName, maxLength) {
    if (fileName.length <= maxLength) return fileName;

    const ext = fileName.substring(fileName.lastIndexOf('.'));
    const name = fileName.substring(0, fileName.lastIndexOf('.'));
    const truncatedName = name.substring(0, maxLength - ext.length - 3) + '...';

    return truncatedName + ext;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 性能监控
function measurePerformance(name, func) {
    return async function(...args) {
        const start = performance.now();
        const result = await func.apply(this, args);
        const end = performance.now();
        console.log(`${name} 执行时间: ${(end - start).toFixed(2)}ms`);
        return result;
    };
}

// 内存清理
function cleanupResources() {
    // 清理未使用的图片引用
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        if (!img.parentNode) {
            img.src = '';
        }
    });

    // 强制垃圾回收（如果可用）
    if (window.gc) {
        window.gc();
    }
}

// 显示帮助信息
function showHelp() {
    const helpText = `
照片精选系统 - 操作帮助

基本操作：
• 单击照片：预览照片
• 双击照片：添加到精修（仅底片区有效）
• 预览页面点"选择"按钮：添加到精修
• 预览页面点"删除"按钮：从精修区删除
• 右键照片：选择相框（精修区）

图片预览：
• ESC：关闭预览
• ←→：切换上下张
• 滚轮：缩放图片
• 双击：适应屏幕/放大
• 拖拽：移动图片
• E键：加入精修

全局快捷键：
• Ctrl+I：导入照片
• Ctrl+Enter：确认提交

操作流程：
1. 点击"选择套餐"按钮选择套餐
2. 导入照片文件夹
3. 双击底片区照片添加到精修，或在预览页面点"选择"按钮
4. 右键精修区照片选择相框（可选）
5. 如需删除，点击预览页面的"删除"按钮
6. 确认提交

注意：必须先选择套餐才能导入照片。
    `.trim();

    alert(helpText);
}

// 显示套餐选择模态框
function showPackageModal() {
    elements.packageModal.style.display = 'block';
    elements.packageModal.style.pointerEvents = 'auto'; // 确保可交互
}

// 隐藏套餐选择模态框
function hidePackageModal() {
    elements.packageModal.style.display = 'none';
    // 防止隐藏后仍拦截事件（Electron/Windows 兼容）
    elements.packageModal.style.pointerEvents = 'none';
}
// 全局提供一个安全关闭函数（ESC使用），保证恢复其它模态的pointer-events
window.safelyCloseIndependentCustomerModal = function() {
    const modal = document.getElementById('independent-customer-modal');
    if (modal && modal.parentNode) {
        modal.parentNode.removeChild(modal);
    }
    elements.packageModal && (elements.packageModal.style.pointerEvents = 'auto');
    elements.submitSuccessModal && (elements.submitSuccessModal.style.pointerEvents = 'auto');
    elements.adminPasswordModal && (elements.adminPasswordModal.style.pointerEvents = 'auto');
};


// 选择套餐
function selectPackage(packageId) {
    const pkg = appState.availablePackages.find(p => p.id === packageId);
    if (!pkg) return;

    // 检查是否需要在选片之前选择套餐
    let shouldResetAfter = false;
    if (appState.originalImages.length > 0) {
        // 使用自定义 confirm，避免 Electron 原生对话框导致的焦点问题
        appConfirm('更换套餐将清空已选择的照片，是否继续？').then(ok => {
            if (!ok) return;
            // 先标记，待确认姓名后再执行重置与重渲染，避免阻塞输入框聚焦
            appState._shouldResetAfterPackageChange = true;
            // 关闭套餐选择模态框，显示客户表单
            hidePackageModal();
            requestAnimationFrame(() => showCustomerNameModal(pkg));
        });
        return; // 异步链路提前返回
    }
    appState._shouldResetAfterPackageChange = shouldResetAfter;
    // 先关闭套餐选择模态框，避免遮挡与焦点干扰
    hidePackageModal();

    // 立即显示客户名字输入模态框，避免因重渲染阻塞
    requestAnimationFrame(() => {
        showCustomerNameModal(pkg);
    });
}

// 存储当前选择的套餐信息（供确认时使用）
let currentSelectedPackage = null;

// 显示客户名字输入模态框 - 使用完全独立的实现
function showCustomerNameModal(pkg) {
    // 确保其它模态彻底禁用指针事件
    elements.packageModal && (elements.packageModal.style.pointerEvents = 'none');
    elements.submitSuccessModal && (elements.submitSuccessModal.style.pointerEvents = 'none');
    elements.adminPasswordModal && (elements.adminPasswordModal.style.pointerEvents = 'none');
    // 存储套餐信息供后续使用
    currentSelectedPackage = pkg;

    // 创建完全独立的模态框，不依赖现有CSS
    const modal = document.createElement('div');
    modal.id = 'independent-customer-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        z-index: 999999;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        min-width: 600px;
        max-width: 800px;
        text-align: left;
        font-family: Arial, sans-serif;
        -webkit-app-region: no-drag;
    `;

    // 获取第一张图片的拍摄时间
    let shootingTime = '待获取';
    if (appState.originalImages.length > 0) {
        getImageShootingTime(appState.originalImages[0].path).then(time => {
            if (time) {
                const shootingTimeElement = document.getElementById('shooting-time-display');
                if (shootingTimeElement) {
                    shootingTimeElement.textContent = time;
                }
            }
        });
    }

    // 创建表单HTML
    content.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
            <div style="flex: 1;">
                <div style="display: flex; gap: 20px; margin-bottom: 15px;">
                    <div>
                        <label style="font-weight: bold; margin-right: 10px;">姓名：</label>
                        <input type="text" id="customer-name-input" placeholder="请输入客户姓名" maxlength="20"
                               style="border: none; border-bottom: 1px solid #ccc; padding: 5px; font-size: 16px; width: 150px;">
                    </div>
                    <div>
                        <label style="font-weight: bold; margin-right: 10px;">套餐：</label>
                        <span style="color: #333;">${pkg.name}</span>
                    </div>
                    <div>
                        <label style="font-weight: bold; margin-right: 10px;">精修张数：</label>
                        <span style="color: #333;">${pkg.refine_count}张</span>
                    </div>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="font-weight: bold; margin-right: 10px;">拍摄日期：</label>
                    <span id="shooting-time-display" style="color: #666;">${shootingTime}</span>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="font-weight: bold; margin-right: 10px;">增加精修：</label>
                    <input type="number" id="additional-refine-input" min="0" value="0"
                           style="border: none; border-bottom: 1px solid #ccc; padding: 5px; font-size: 16px; width: 80px;">
                    <span style="margin-left: 5px; color: #999;">张</span>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="font-weight: bold; margin-right: 10px;">是否续订套餐：</label>
                    <label style="margin-right: 15px;"><input type="radio" name="renew-package" value="yes" style="margin-right: 5px;">是</label>
                    <label><input type="radio" name="renew-package" value="no" checked style="margin-right: 5px;">否</label>
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="font-weight: bold; margin-right: 10px;">照片可否让我们展示：</label>
                    <label style="margin-right: 15px;"><input type="radio" name="allow-display" value="yes" style="margin-right: 5px;">是</label>
                    <label><input type="radio" name="allow-display" value="no" checked style="margin-right: 5px;">否</label>
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="font-weight: bold; display: block; margin-bottom: 10px;">备注：</label>
                    <textarea id="notes-input" placeholder="请输入备注信息..."
                              style="width: 100%; height: 80px; border: 1px solid #ccc; border-radius: 4px; padding: 10px; resize: vertical; font-family: Arial, sans-serif;"></textarea>
                </div>

                <div style="font-size: 12px; color: #666; line-height: 1.4; margin-bottom: 20px;">
                    <div>1.精修5天内出第一版</div>
                    <div>（第一版精修需自行挑选，到时候会给您发到微信，操作等）</div>
                    <div>2.底片10天内发送到微信</div>
                    <div>（底片为原图，不修饰，高清本不会再修改，切记要保存）</div>
                    <div>3.产品大概2个月左右到货，到时候会通知您（市区内大概5）</div>
                </div>

                <div style="font-weight: bold; margin-bottom: 10px;">
                    本次拍摄已完成，底片已确认无误。后续将进行修图环节；（客户确定签字）
                </div>

                <div style="margin-bottom: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;">
                        <label style="font-weight: bold; color: red;">客户签名：</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <button type="button" id="clear-signature-btn" style="
                                background: #f0f0f0;
                                border: 1px solid #ccc;
                                padding: 5px 15px;
                                border-radius: 4px;
                                cursor: pointer;
                                font-size: 12px;
                            ">清除签名</button>
                            <span style="font-size: 12px; color: #666;">请在下方区域内签名</span>
                        </div>
                    </div>
                    <div id="signature-container" style="border: 2px dashed #ccc; border-radius: 8px; background: #fafafa; height: 200px; position: relative;">
                        <canvas id="signature-canvas" style="
                            width: 100%;
                            height: 100%;
                            background: white;
                            cursor: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 16 16\"><path d=\"M13.5 1a1.5 1.5 0 1 1 0 3l-11 11-4 1 1-4 11-11a1.5 1.5 0 0 1 2.12 0z\" fill=\"%23000\"/></svg>') 0 16, auto;
                            display: block;
                            border-radius: 6px;
                            position: absolute;
                            top: 0;
                            left: 0;
                        "></canvas>
                    </div>
                </div>
            </div>

            <div style="margin-left: 20px;">
                <button id="confirm-contract-btn" style="
                    background: #ff6b6b;
                    color: white;
                    border: none;
                    padding: 15px 30px;
                    border-radius: 8px;
                    font-size: 18px;
                    font-weight: bold;
                    cursor: pointer;
                    box-shadow: 0 4px 8px rgba(255,107,107,0.3);
                ">确定</button>
                <div style="margin-top: 10px; font-size: 12px; color: #666; text-align: center;">
                    点击确定后<br>打开文件夹<br>这个界面也<br>关掉
                </div>
            </div>
        </div>
    `;

    // 添加事件监听器
    const confirmBtn = content.querySelector('#confirm-contract-btn');
    const customerNameInput = content.querySelector('#customer-name-input');
    const additionalRefineInput = content.querySelector('#additional-refine-input');
    const notesInput = content.querySelector('#notes-input');
    const signatureCanvas = content.querySelector('#signature-canvas');
    const clearSignatureBtn = content.querySelector('#clear-signature-btn');

    // 初始化签名功能
    let isDrawing = false;
    let lastX = 0;
    let lastY = 0;
    const ctx = signatureCanvas.getContext('2d');

    // 等待DOM渲染完成后设置画布尺寸
    setTimeout(() => {
        const container = signatureCanvas.parentElement;
        const containerRect = container.getBoundingClientRect();

        // 设置画布尺寸
        signatureCanvas.width = containerRect.width;
        signatureCanvas.height = containerRect.height;

        // 设置白色背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, signatureCanvas.width, signatureCanvas.height);

        // 设置画笔样式
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
    }, 100);

    // 鼠标事件
    signatureCanvas.addEventListener('mousedown', (e) => {
        isDrawing = true;
        const rect = signatureCanvas.getBoundingClientRect();
        lastX = e.clientX - rect.left;
        lastY = e.clientY - rect.top;
    });

    signatureCanvas.addEventListener('mousemove', (e) => {
        if (!isDrawing) return;
        const rect = signatureCanvas.getBoundingClientRect();
        const currentX = e.clientX - rect.left;
        const currentY = e.clientY - rect.top;

        ctx.beginPath();
        ctx.moveTo(lastX, lastY);
        ctx.lineTo(currentX, currentY);
        ctx.stroke();

        lastX = currentX;
        lastY = currentY;
    });

    signatureCanvas.addEventListener('mouseup', () => {
        isDrawing = false;
    });

    signatureCanvas.addEventListener('mouseleave', () => {
        isDrawing = false;
    });

    // 触摸事件支持（移动设备）
    signatureCanvas.addEventListener('touchstart', (e) => {
        e.preventDefault();
        isDrawing = true;
        const rect = signatureCanvas.getBoundingClientRect();
        const touch = e.touches[0];
        lastX = touch.clientX - rect.left;
        lastY = touch.clientY - rect.top;
    });

    signatureCanvas.addEventListener('touchmove', (e) => {
        e.preventDefault();
        if (!isDrawing) return;
        const rect = signatureCanvas.getBoundingClientRect();
        const touch = e.touches[0];
        const currentX = touch.clientX - rect.left;
        const currentY = touch.clientY - rect.top;

        ctx.beginPath();
        ctx.moveTo(lastX, lastY);
        ctx.lineTo(currentX, currentY);
        ctx.stroke();

        lastX = currentX;
        lastY = currentY;
    });

    signatureCanvas.addEventListener('touchend', (e) => {
        e.preventDefault();
        isDrawing = false;
    });

    // 清除签名按钮
    clearSignatureBtn.addEventListener('click', () => {
        ctx.clearRect(0, 0, signatureCanvas.width, signatureCanvas.height);
        // 重新设置白色背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, signatureCanvas.width, signatureCanvas.height);
        // 恢复画笔样式
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
    });

    const closeModal = () => {
        if (modal.parentNode) {
            modal.parentNode.removeChild(modal);
        }
        // 恢复其他模态的指针事件
        elements.packageModal && (elements.packageModal.style.pointerEvents = 'auto');
        elements.submitSuccessModal && (elements.submitSuccessModal.style.pointerEvents = 'auto');
        elements.adminPasswordModal && (elements.adminPasswordModal.style.pointerEvents = 'auto');
    };

    const handleConfirm = async () => {
        const customerName = customerNameInput.value.trim();
        if (!customerName) {
            alert('请输入客户姓名');
            customerNameInput.focus();
            return;
        }

        if (!/^[\u4e00-\u9fa5a-zA-Z0-9\s\.\-_]{1,20}$/.test(customerName)) {
            alert('客户姓名格式不正确，请使用中文、英文、数字，长度不超过20个字符');
            customerNameInput.focus();
            return;
        }

        // 检查签名是否存在
        const signatureCanvas = content.querySelector('#signature-canvas');
        if (signatureCanvas) {
            const ctx = signatureCanvas.getContext('2d');
            const imageData = ctx.getImageData(0, 0, signatureCanvas.width, signatureCanvas.height);
            const data = imageData.data;

            // 检查是否有非白色像素（即是否有签名）
            let hasSignature = false;
            for (let i = 0; i < data.length; i += 4) {
                // 检查RGB值，如果不是白色(255,255,255)则认为有签名
                if (data[i] !== 255 || data[i + 1] !== 255 || data[i + 2] !== 255) {
                    hasSignature = true;
                    break;
                }
            }

            if (!hasSignature) {
                alert('请先进行客户签名');
                return;
            }
        }

        // 收集表单数据
        const additionalRefine = parseInt(additionalRefineInput.value) || 0;
        const renewPackage = content.querySelector('input[name="renew-package"]:checked')?.value === 'yes';
        const allowDisplay = content.querySelector('input[name="allow-display"]:checked')?.value === 'yes';
        const notes = notesInput.value.trim();
        const shootingTime = content.querySelector('#shooting-time-display')?.textContent || '';

        // 执行确认逻辑
        const finalPkg = currentSelectedPackage;
        appState.currentPackage = {
            id: finalPkg.id,
            price: finalPkg.price,
            count: finalPkg.refine_count,
            name: finalPkg.name,
            image1_path: finalPkg.image1_path,
            image2_path: finalPkg.image2_path,
            customerName: customerName,
            additionalRefine: additionalRefine,
            renewPackage: renewPackage,
            allowDisplay: allowDisplay,
            notes: notes,
            shootingTime: shootingTime
        };

        // 先确保精修文件夹存在，然后截图保存表单
        try {
            // 创建精修文件夹（如果不存在）
            if (ipcRenderer && appState.sourceFolderPath) {
                const packagePrice = finalPkg.price;
                const folderName = `${customerName}十${packagePrice}`;
                const orderInfo = {
                    package: finalPkg,
                    customerName: customerName,
                    folderName: folderName
                };

                // 确保精修文件夹存在
                await ipcRenderer.invoke('export-selected-images', {
                    sourceFolderPath: appState.sourceFolderPath,
                    selectedImages: [], // 空数组，只是为了创建文件夹
                    orderInfo
                });
            }

            // 截整个窗体（独立弹窗），保证样式一致
            await captureAndSaveContract(modal, customerName);
        } catch (error) {
            console.error('保存合同截图失败:', error);
            showToast('合同截图保存失败', 'error');
        }

        // 在Electron下，弹出后请求主进程进行一次聚焦重置（模拟你手动最小化/还原）
        if (typeof ipcRenderer !== 'undefined') {
            setTimeout(() => {
                ipcRenderer.invoke('force-window-refocus');
            }, 50);
        }

        closeModal();

        // 执行重置和更新
        if (appState._shouldResetAfterPackageChange) {
            appState.selectedImages = [];
            appState.selectedImageForFrame = null;
            requestAnimationFrame(() => {
                renderSelectedGallery();
                renderOriginalGallery();
                updatePackageDisplay();
                updateUI();
            });
        } else {
            updatePackageDisplay();
            updateUI();
        }

        showToast(`已为客户 "${customerName}" 选择 ${finalPkg.name}`);

        setTimeout(() => {
            showPromotionAd();
        }, 200);
    };

    // 创建遮罩层（单独的可点击层），并确保内容区拥有更高层级与指针事件
    const mask = document.createElement('div');
    mask.style.cssText = `
        position: fixed;
        top: 0; left: 0; right: 0; bottom: 0;
        background: rgba(0,0,0,0.8);
        z-index: 1;
    `;
    // 确保模态框在最顶层并且可接收事件
    modal.style.zIndex = '2147483647';
    content.style.pointerEvents = 'auto';
    content.style.position = 'relative';
    content.style.zIndex = '2';

    // 绑定事件
    confirmBtn.addEventListener('click', handleConfirm);

    // 点击遮罩层关闭
    mask.addEventListener('click', () => closeModal());

    // 阻止内容区域事件冒泡，避免被其他全局处理器干扰
    content.addEventListener('click', (e) => {
        e.stopPropagation();
    });
    content.addEventListener('mousedown', (e) => {
        e.stopPropagation();
    });

    // 回车键确认
    customerNameInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            handleConfirm();
        }
    });

    // 添加遮罩和内容到模态根元素
    modal.appendChild(mask);
    modal.appendChild(content);
    document.body.appendChild(modal);

    // 修复Electron中所有输入框的焦点问题
    const allInputs = content.querySelectorAll('input, textarea');
    allInputs.forEach(input => {
        // 设置tabindex确保可以获得焦点
        input.setAttribute('tabindex', '0');

        // 添加多种事件确保能够获得焦点
        input.addEventListener('click', function(e) {
            e.stopPropagation();
            setTimeout(() => {
                this.focus();
            }, 0);
        });

        // 添加鼠标按下事件
        input.addEventListener('mousedown', function(e) {


            e.stopPropagation();
            setTimeout(() => {
                this.focus();
            }, 0);
        });

        // 添加鼠标抬起事件作为备用
        input.addEventListener('mouseup', function(e) {
            e.stopPropagation();
            if (document.activeElement !== this) {
                setTimeout(() => {
                    this.focus();
                }, 10);
            }
        });
    });

    // 修复Electron Windows焦点问题 - 强制窗口获得焦点
    if (ipcRenderer) {
        // 通知主进程强制窗口获得焦点
        ipcRenderer.invoke('force-window-focus').then(() => {
            // 窗口获得焦点后，再聚焦到输入框
            setTimeout(() => {
                customerNameInput.focus();
                customerNameInput.select();
            }, 50);
        });
    } else {
        // 浏览器环境的备用方案
        setTimeout(() => {
            customerNameInput.focus();
            customerNameInput.select();
        }, 100);
    }
}

// 隐藏客户名字输入模态框 (完全参照hideAdminPasswordModal)
function hideCustomerNameModal() {
    elements.customerNameModal.style.display = 'none';
    elements.customerNameInput.value = '';
}

// 确认客户名字并选择套餐 (参照verifyAdminPassword的简洁性)
function confirmCustomerName() {
    const customerName = elements.customerNameInput.value.trim();

    if (!customerName) {
        alert('请输入客户姓名');
        return;
    }

    // 验证客户名字
    if (!/^[\u4e00-\u9fa5a-zA-Z0-9\s\.\-_]{1,20}$/.test(customerName)) {
        alert('客户姓名格式不正确，请使用中文、英文、数字，长度不超过20个字符');
        return;
    }

    if (!currentSelectedPackage) {
        alert('套餐信息丢失，请重新选择套餐');
        hideCustomerNameModal();
        return;
    }

    const pkg = currentSelectedPackage;

    // 存储客户信息和套餐信息
    appState.currentPackage = {
        id: pkg.id,
        price: pkg.price,
        count: pkg.refine_count,
        name: pkg.name,
        image1_path: pkg.image1_path,
        image2_path: pkg.image2_path,
        customerName: customerName
    };

    // 先关闭弹窗，确保焦点与视觉反馈正常
    hideCustomerNameModal();
    hidePackageModal();

    // 若选择前已有图片，且用户确认更换套餐，则在此时再执行重置与重渲染，避免阻塞输入框聚焦
    if (appState._shouldResetAfterPackageChange) {
        appState.selectedImages = [];
        appState.selectedImageForFrame = null;
        // 使用 rAF 将大批量渲染切到下一帧，避免卡顿影响体验
        requestAnimationFrame(() => {
            renderSelectedGallery();
            renderOriginalGallery();
            updatePackageDisplay();
            updateUI();
        });
    } else {
        updatePackageDisplay();
        updateUI();
    }

    showToast(`已为客户 "${customerName}" 选择 ${pkg.name}`);

    // 确定套餐后显示宣传广告
    setTimeout(() => {
        showPromotionAd();
    }, 200);
}

// 显示宣传广告
async function showPromotionAd() {
    try {
        let settings;

        // 从数据库获取宣传广告设置
        if (ipcRenderer) {
            settings = await ipcRenderer.invoke('get-promotion-ad-settings');
        } else {
            // 浏览器环境的默认设置
            settings = {
                enabled: true,
                image_path: null,
                title: '感谢选择我们的服务！',
                description: '恭喜选择套餐！'
            };
        }

        // 如果宣传广告功能被禁用，则不显示
        if (!settings.enabled) {
            return;
        }

        // 创建宣传广告模态框
        const modal = document.createElement('div');
        modal.id = 'promotion-modal';
        modal.className = 'promotion-modal';

        if (settings.image_path) {
            // 有自定义图片时显示图片
            modal.innerHTML = `
                <div class="promotion-modal-content">
                    <button class="promotion-close" onclick="hidePromotionAd()">&times;</button>
                    <img src="file://${settings.image_path}" alt="宣传广告" class="promotion-image"
                         onerror="this.style.display='none'; showDefaultPromotion()">
                    <div class="promotion-title">${settings.title}</div>
                </div>
            `;
        } else {
            // 没有图片时显示默认内容
            showDefaultPromotionContent(modal, settings);
        }

        document.body.appendChild(modal);
        modal.style.display = 'block';

        // 点击模态框背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                hidePromotionAd();
            }
        });

    } catch (error) {
        console.error('显示宣传广告失败:', error);
        // 如果出错，显示默认内容
        showDefaultPromotionFallback();
    }
}

// 隐藏宣传广告
function hidePromotionAd() {
    const modal = document.getElementById('promotion-modal');
    if (modal) {
        modal.remove();
    }
}

// 显示默认宣传内容（无图片时）
function showDefaultPromotionContent(modal, settings) {
    modal.innerHTML = `
        <div class="promotion-modal-content">
            <button class="promotion-close" onclick="hidePromotionAd()">&times;</button>
            <div style="padding: 40px; color: white; text-align: center;">
                <h2 style="margin-bottom: 20px; color: #007bff;">🎉 ${settings.title}</h2>
                <p style="font-size: 18px; line-height: 1.6; margin-bottom: 15px;">
                    ${settings.description}
                </p>
                <p style="font-size: 16px; color: #ffc107;">
                    💝 好评返现活动正在进行中
                </p>
                <p style="font-size: 14px; margin-top: 20px; opacity: 0.8;">
                    请在管理后台的"系统设置"中上传宣传图片
                </p>
            </div>
        </div>
    `;
}

// 显示默认宣传内容（当图片加载失败时）
function showDefaultPromotion() {
    const modal = document.getElementById('promotion-modal');
    if (modal) {
        const content = modal.querySelector('.promotion-modal-content');
        content.innerHTML = `
            <button class="promotion-close" onclick="hidePromotionAd()">&times;</button>
            <div style="padding: 40px; color: white; text-align: center;">
                <h2 style="margin-bottom: 20px; color: #007bff;">🎉 恭喜选择套餐！</h2>
                <p style="font-size: 18px; line-height: 1.6; margin-bottom: 15px;">
                    感谢您的信任！我们将为您提供专业的照片精修服务。
                </p>
                <p style="font-size: 16px; color: #ffc107;">
                    💝 好评返现活动正在进行中
                </p>
                <p style="font-size: 14px; margin-top: 20px; opacity: 0.8;">
                    请在管理后台的"系统设置"中上传宣传图片
                </p>
            </div>
        `;
    }
}

// 显示宣传广告失败时的备用方案
function showDefaultPromotionFallback() {
    const modal = document.createElement('div');
    modal.id = 'promotion-modal';
    modal.className = 'promotion-modal';

    modal.innerHTML = `
        <div class="promotion-modal-content">
            <button class="promotion-close" onclick="hidePromotionAd()">&times;</button>
            <div style="padding: 40px; color: white; text-align: center;">
                <h2 style="margin-bottom: 20px; color: #007bff;">🎉 恭喜选择套餐！</h2>
                <p style="font-size: 18px; line-height: 1.6; margin-bottom: 15px;">
                    感谢您的信任！我们将为您提供专业的照片精修服务。
                </p>
                <p style="font-size: 16px; color: #ffc107;">
                    💝 好评返现活动正在进行中
                </p>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'block';

    // 点击模态框背景关闭
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            hidePromotionAd();
        }
    });
}

// 更新套餐显示
function updatePackageDisplay() {
    if (appState.currentPackage) {
        // 隐藏选择按钮，显示已选择的套餐信息
        elements.selectPackageBtn.style.display = 'none';
        elements.selectedPackageInfo.style.display = 'block';

        const selectedPackageName = elements.selectedPackageInfo.querySelector('.selected-package-name');
        const customerName = appState.currentPackage.customerName;
        const packageDisplayName = customerName ? `${customerName} - ${appState.currentPackage.name}` : appState.currentPackage.name;
        selectedPackageName.textContent = packageDisplayName;

        // 更新套餐内容描述，包含客户名字信息
        let description = `${appState.currentPackage.name}<br>包含${appState.currentPackage.count}张精修照片`;
        if (customerName) {
            description += `<br><small style="color: #007bff;">客户：${customerName}</small>`;
        }
        elements.packageDescription.innerHTML = description;

        // 显示套餐产品图片
        displayPackageProductImages();
    } else {
        // 显示选择按钮，隐藏已选择的套餐信息
        elements.selectPackageBtn.style.display = 'block';
        elements.selectedPackageInfo.style.display = 'none';
        elements.packageDescription.textContent = '请选择套餐';

        // 隐藏产品图片
        elements.packageProductImages.style.display = 'none';
    }
}

// 显示套餐产品图片
function displayPackageProductImages() {
    if (!appState.currentPackage) {
        elements.packageProductImages.style.display = 'none';
        return;
    }

    const container = elements.packageProductImages.querySelector('.product-images-container');
    container.innerHTML = '';

    // 检查是否有产品图片
    if (appState.currentPackage.image1_path || appState.currentPackage.image2_path) {
        let imagesHTML = '<div class="package-product-images-grid">';

        if (appState.currentPackage.image1_path) {
            imagesHTML += `
                <div class="product-image-container">
                    <img src="" data-image-path="${appState.currentPackage.image1_path}"
                         alt="${appState.currentPackage.name}产品图1"
                         class="product-image"
                         onclick="showImageLightbox(this)">
                </div>`;
        }

        if (appState.currentPackage.image2_path) {
            imagesHTML += `
                <div class="product-image-container">
                    <img src="" data-image-path="${appState.currentPackage.image2_path}"
                         alt="${appState.currentPackage.name}产品图2"
                         class="product-image"
                         onclick="showImageLightbox(this)">
                </div>`;
        }

        imagesHTML += '</div>';
        container.innerHTML = imagesHTML;

        // 显示产品图片容器
        elements.packageProductImages.style.display = 'block';

        // 加载图片
        loadPackageImages(container);
    } else {
        // 如果没有产品图片，隐藏容器
        elements.packageProductImages.style.display = 'none';
    }
}

// 显示相框右键菜单
function showFrameContextMenu(x, y) {
    const menu = elements.frameContextMenu;
    if (!menu) {
        console.error('右键菜单元素未找到');
        return;
    }

    console.log('显示右键菜单，位置:', x, y);

    // 确保菜单在正确的层级显示
    menu.style.display = 'block';
    menu.style.position = 'fixed';
    menu.style.zIndex = '1001';

    // 先设置初始位置，然后获取实际尺寸
    menu.style.left = x + 'px';
    menu.style.top = y + 'px';

    // 调整菜单位置，确保不超出屏幕边界
    setTimeout(() => {
        const menuRect = menu.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        let left = x;
        let top = y;

        if (left + menuRect.width > viewportWidth) {
            left = viewportWidth - menuRect.width - 10;
        }

        if (top + menuRect.height > viewportHeight) {
            top = viewportHeight - menuRect.height - 10;
        }

        menu.style.left = left + 'px';
        menu.style.top = top + 'px';

        console.log('右键菜单最终位置:', left, top);
        console.log('菜单尺寸:', menuRect.width, 'x', menuRect.height);

        // 检查菜单内容
        const menuItems = menu.querySelectorAll('.context-menu-item');
        console.log('菜单项数量:', menuItems.length);
        menuItems.forEach((item, index) => {
            console.log(`菜单项 ${index + 1}:`, item.textContent, item.className);
        });
    }, 0);
}

// 隐藏相框右键菜单
function hideFrameContextMenu() {
    elements.frameContextMenu.style.display = 'none';
    // 隐藏所有子菜单并清理监听器
    const allSubmenus = elements.frameContextMenu.querySelectorAll('.context-submenu');
    allSubmenus.forEach(submenu => {
        submenu.style.display = 'none';
        // 清理动态监听器
        if (submenu.adjustPosition) {
            window.removeEventListener('resize', submenu.adjustPosition);
            window.removeEventListener('scroll', submenu.adjustPosition, true);
        }
    });
}

// 设置图片相框
function setImageFrame(frameName) {
    if (!appState.selectedImageForFrame) return;

    const imageIndex = appState.selectedImages.findIndex(img => img.id === appState.selectedImageForFrame.id);
    if (imageIndex !== -1) {
        appState.selectedImages[imageIndex].frame = frameName;
        renderSelectedGallery();

        const frameText = frameName ? `相框: ${frameName}` : '已移除相框';
        showToast(`${appState.selectedImageForFrame.name} ${frameText}`);
        scheduleAutoSaveNow();
    }
}

// 显示添加自定义相框类型对话框
function showAddCustomCategoryDialog() {
    const categoryName = prompt('请输入新的相框类型名称:', '');
    if (!categoryName || categoryName.trim() === '') {
        return;
    }

    const frameName = prompt('请输入相框名称:', '');
    if (!frameName || frameName.trim() === '') {
        return;
    }

    const frameDescription = prompt('请输入相框描述:', '');

    // 创建新相框对象
    const newFrame = {
        id: Date.now(), // 使用时间戳作为临时ID
        name: frameName.trim(),
        description: frameDescription ? frameDescription.trim() : '',
        category: categoryName.trim()
    };

    // 添加到可用相框列表
    appState.availableFrames.push(newFrame);

    // 重新渲染相框菜单
    renderFrames(appState.availableFrames);

    // 显示成功提示
    showToast(`已添加新相框: ${newFrame.name} (${newFrame.category})`);

    // 注意：这里添加的是临时相框，重新加载页面后会消失
    // 如果需要持久保存，需要调用后端API
}

// 显示管理密码验证模态框
function showAdminPasswordModal() {
    elements.adminPasswordModal.style.display = 'block';
    elements.adminPasswordInput.value = '';
    elements.adminPasswordInput.focus();
}

// 隐藏管理密码验证模态框
function hideAdminPasswordModal() {
    elements.adminPasswordModal.style.display = 'none';
    elements.adminPasswordInput.value = '';
}

// 验证管理密码
function verifyAdminPassword() {
    const password = elements.adminPasswordInput.value;
    const correctPassword = '147258369';

    if (password === correctPassword) {
        hideAdminPasswordModal();
        openAdminPanel();
    } else {
        showToast('密码错误，请重试', 'error');
        elements.adminPasswordInput.value = '';
        elements.adminPasswordInput.focus();
    }
}

// 打开管理后台
function openAdminPanel() {
    window.location.href = 'admin.html';
}

// 更新选中图片名字显示
function updateSelectedImageName(imageName) {
    if (imageName) {
        elements.selectedImageName.textContent = imageName;
    } else {
        elements.selectedImageName.textContent = '';
    }
}

// 显示提交成功确认界面
function showSubmitSuccessModal() {
    elements.submitSuccessModal.classList.add('show');
    elements.submitSuccessModal.style.display = 'flex';
}

// 隐藏提交成功确认界面
function hideSubmitSuccessModal() {
    elements.submitSuccessModal.classList.remove('show');
    elements.submitSuccessModal.style.display = 'none';
}

// ========== 自动保存与会话恢复 ==========
function buildSessionSnapshot() {
    // 仅保存必要字段，避免过大
    return {
        sourceFolderPath: appState.sourceFolderPath,
        currentPackage: appState.currentPackage,
        selectedSortMode: appState.selectedSortMode,
        // 只存储原始图片的 name/path/id，避免冗余
        originalImages: appState.originalImages.map(img => ({ id: img.id, name: img.name, path: img.path })),
        // 选中图片需要保存 frame 与 selectedAt
        selectedImages: appState.selectedImages.map(img => ({ id: img.id, name: img.name, path: img.path, frame: img.frame || null, selectedAt: img.selectedAt || null })),
        // 回收站图片
        recycledImages: appState.recycledImages.map(img => ({ id: img.id, name: img.name, path: img.path, deletedAt: img.deletedAt || null, deletedFrom: img.deletedFrom || null })),
        timestamp: Date.now()
    };
}

async function saveSessionSnapshot() {
    if (!ipcRenderer) return; // 仅在Electron下持久化
    try {
        const snapshot = buildSessionSnapshot();
        const result = await ipcRenderer.invoke('save-session', snapshot);
        if (!result || !result.success) {
            console.warn('会话保存可能失败:', result && result.error);
        }
    } catch (error) {
        console.warn('保存会话出错:', error);
    }
}

function startAutoSave() {
    if (autosaveTimerId) clearInterval(autosaveTimerId);
    autosaveTimerId = setInterval(() => {
        saveSessionSnapshot();
    }, 10000); // 每10秒保存一次
}

// 立刻触发一次保存（节流）
const scheduleAutoSaveNow = throttle(() => {
    saveSessionSnapshot();
}, 1500);

async function restorePreviousSession() {
    if (!ipcRenderer) return;
    try {
        const data = await ipcRenderer.invoke('load-session');
        if (!data) return;
        // 恢复基础状态
        appState.sourceFolderPath = data.sourceFolderPath || null;
        appState.currentPackage = data.currentPackage || null;
        appState.selectedSortMode = data.selectedSortMode || 'filename';

        // 恢复图片与选择
        appState.originalImages = Array.isArray(data.originalImages) ? data.originalImages : [];
        appState.selectedImages = Array.isArray(data.selectedImages) ? data.selectedImages : [];
        appState.recycledImages = Array.isArray(data.recycledImages) ? data.recycledImages : [];

        // 渲染相关UI
        if (appState.originalImages.length > 0) {
            renderOriginalGallery();
        }
        if (appState.selectedImages.length > 0) {
            renderSelectedGallery();
        }
        updatePackageDisplay();
        updateUI();

        // 给个轻提示
        showToast('已恢复上次进度', 'info');
    } catch (error) {
        console.warn('恢复会话失败:', error);
    }
}

// 调试和测试功能
window.PhotoApp = {
    // 切换虚拟滚动模式
    toggleVirtualScroll: () => {
        appState.useVirtualScroll = !appState.useVirtualScroll;
        console.log('虚拟滚动模式:', appState.useVirtualScroll ? '开启' : '关闭');

        if (appState.originalImages.length > 0) {
            renderOriginalGallery();
            showToast(`虚拟滚动已${appState.useVirtualScroll ? '开启' : '关闭'}`, 'info');
        }
    },

    // 获取应用状态
    getState: () => {
        return {
            imageCount: appState.originalImages.length,
            selectedCount: appState.selectedImages.length,
            useVirtualScroll: appState.useVirtualScroll,
            hasVirtualScrollInstance: !!appState.originalGalleryVirtualScroll
        };
    },

    // 强制切换到虚拟滚动模式（用于测试）
    forceVirtualScroll: () => {
        appState.useVirtualScroll = true;
        if (appState.originalImages.length > 0) {
            renderOriginalGalleryWithVirtualScroll();
            showToast('已强制启用虚拟滚动', 'info');
        }
    },

    // 强制切换到传统模式（用于测试）
    forceTraditional: () => {
        if (appState.originalGalleryVirtualScroll) {
            appState.originalGalleryVirtualScroll.destroy();
            appState.originalGalleryVirtualScroll = null;
        }

        if (appState.originalImages.length > 0) {
            renderOriginalGalleryTraditional();
            showToast('已切换到传统模式', 'info');
        }
    },

    // 生成指定数量的测试图片
    generateTestImages: (count = 100) => {
        if (!appState.currentPackage) {
            alert('请先选择套餐');
            return;
        }

        const mockImages = [];
        for (let i = 1; i <= count; i++) {
            mockImages.push({
                name: `TEST_${String(i).padStart(4, '0')}.jpg`,
                path: `https://picsum.photos/400/300?random=${i + 1000}`,
                id: generateId()
            });
        }

        appState.originalImages = mockImages;
        appState.sourceFolderPath = '/test/photos';

        renderOriginalGallery();
        updateUI();
        showToast(`已生成 ${count} 张测试照片`, 'info');

        console.log(`已生成 ${count} 张测试照片，虚拟滚动模式: ${appState.useVirtualScroll ? '开启' : '关闭'}`);
    },

    // 测试预览性能
    testPreviewPerformance: async () => {
        if (appState.originalImages.length === 0) {
            console.log('请先导入图片再测试预览性能');
            return;
        }

        const testImage = appState.originalImages[0];
        console.log('🔍 测试动态加载预览性能...');
        console.time('预览启动时间');

        try {
            await showLightbox(testImage, 'original');
            console.timeEnd('预览启动时间');
            console.log('✅ 动态加载预览测试完成');
            console.log('💾 内存优势：只加载当前+前后各2张图片，最多缓存7张');
            console.log('⚡ 性能优势：不再创建所有图片DOM元素，避免内存崩溃');
        } catch (error) {
            console.timeEnd('预览启动时间');
            console.error('❌ 预览测试失败:', error);
        }
    },

    // 测试大量图片性能（专门解决300+图片崩溃问题）
    testMassiveImages: async (count = 300) => {
        console.log(`🧪 开始测试 ${count} 张图片的预览性能...`);

        if (!appState.currentPackage) {
            console.log('自动选择第一个套餐进行测试...');
            if (appState.availablePackages.length > 0) {
                selectPackage(appState.availablePackages[0].id);
            } else {
                console.log('未找到可用套餐，请先初始化数据');
                return;
            }
        }

        // 生成大量测试图片
        const mockImages = [];
        for (let i = 1; i <= count; i++) {
            mockImages.push({
                name: `MASSIVE_TEST_${String(i).padStart(4, '0')}.jpg`,
                path: `https://picsum.photos/800/600?random=${i + 2000}`,
                id: generateId()
            });
        }

        appState.originalImages = mockImages;
        appState.sourceFolderPath = '/massive/test/photos';

        // 渲染画廊
        console.time('画廊渲染时间');
        renderOriginalGallery();
        updateUI();
        console.timeEnd('画廊渲染时间');

        console.log(`✅ 画廊渲染完成，共 ${count} 张图片`);
        console.log(`📊 内存使用对比预估：`);
        console.log(`   传统模式预估: ${count} 个DOM元素 + ${count} 个图片 = ${Math.round(count * 2)} MB`);
        console.log(`   动态模式实际: 最多7个DOM元素 + 智能缓存 ≈ 10-20 MB`);

        // 测试预览启动
        setTimeout(async () => {
            console.log('⏱️  3秒后开始预览测试...');
            console.time('大量图片预览启动');

            try {
                await showLightbox(mockImages[10], 'original'); // 测试第11张图片
                console.timeEnd('大量图片预览启动');
                console.log(`🎉 成功！${count}张图片预览正常启动，没有崩溃！`);
                console.log('💡 即使是500+张图片也能流畅运行，内存占用极低');
            } catch (error) {
                console.timeEnd('大量图片预览启动');
                console.error('预览启动失败:', error);
            }
        }, 3000);

        showToast(`已生成 ${count} 张测试照片，3秒后测试预览性能`, 'info');
    },

    // 测试滚动条拖拽性能
    testScrollbarDragging: async (count = 500) => {
        console.log('🎯 测试滚动条快速拖拽性能...');

        if (!appState.currentPackage) {
            console.log('自动选择第一个套餐...');
            if (appState.availablePackages.length > 0) {
                selectPackage(appState.availablePackages[0].id);
            }
        }

        // 生成大量测试图片
        const mockImages = [];
        for (let i = 1; i <= count; i++) {
            mockImages.push({
                name: `SCROLL_TEST_${String(i).padStart(4, '0')}.jpg`,
                path: `https://picsum.photos/400/300?random=${i + 3000}`,
                id: generateId()
            });
        }

        appState.originalImages = mockImages;
        appState.sourceFolderPath = '/scroll/test/photos';

        renderOriginalGallery();
        updateUI();

        console.log(`✅ 已生成 ${count} 张测试图片`);
        console.log('🎯 现在请尝试以下操作测试性能改进：');
        console.log('1. 缓慢滚动鼠标滚轮 - 应该流畅加载');
        console.log('2. 快速拖动滚动条到底部 - 不应该白屏或卡顿');
        console.log('3. 快速拖动到中间位置 - 应该快速响应');
        console.log('4. 打开控制台查看渲染日志');

        showToast('滚动条拖拽测试准备完成，请尝试快速拖动！', 'info');

        // 添加性能监控
        if (appState.originalGalleryVirtualScroll) {
            const virtualScroll = appState.originalGalleryVirtualScroll;
            console.log('📊 当前虚拟滚动状态：');
            console.log('- 缓冲区大小:', virtualScroll.options.bufferSize);
            console.log('- 项目总数:', virtualScroll.items.length);
            console.log('- 可见项数:', virtualScroll.visibleItems.size);
        }
    },

    // 监控虚拟滚动性能
    monitorScrollPerformance: () => {
        if (!appState.originalGalleryVirtualScroll) {
            console.log('❌ 虚拟滚动未启用，请先生成图片');
            return;
        }

        const virtualScroll = appState.originalGalleryVirtualScroll;
        console.log('📊 开始监控虚拟滚动性能...');

        // 监控滚动状态
        const originalHandleScrollEvent = virtualScroll.handleScrollEvent;
        let scrollEvents = 0;
        let fastScrollEvents = 0;
        let renderCalls = 0;

        virtualScroll.handleScrollEvent = function() {
            scrollEvents++;
            if (this.scrollVelocity > 2) {
                fastScrollEvents++;
            }
            return originalHandleScrollEvent.call(this);
        };

        const originalRender = virtualScroll.render;
        virtualScroll.render = function() {
            renderCalls++;
            return originalRender.call(this);
        };

        // 10秒后输出统计信息
        setTimeout(() => {
            console.log('📈 滚动性能统计（10秒内）：');
            console.log(`- 滚动事件总数: ${scrollEvents}`);
            console.log(`- 快速滚动事件: ${fastScrollEvents} (${((fastScrollEvents/scrollEvents)*100).toFixed(1)}%)`);
            console.log(`- 渲染调用次数: ${renderCalls}`);
            console.log(`- 当前可见项: ${virtualScroll.visibleItems.size}`);
            console.log(`- 滚动速度: ${virtualScroll.scrollVelocity?.toFixed(2) || 0}`);
            console.log(`- 是否正在拖拽滚动条: ${virtualScroll.isDraggingScrollbar || false}`);

            // 重置监控
            virtualScroll.handleScrollEvent = originalHandleScrollEvent;
            virtualScroll.render = originalRender;

            console.log('✅ 性能监控结束');
        }, 10000);

        console.log('⏱️ 性能监控开始，请在10秒内测试滚动操作...');
        showToast('性能监控已启动，10秒后查看结果', 'info');
    }
};

// 在控制台显示调试信息
console.log(`
🖼️ 照片精选系统调试工具 - 智能滚动优化版本
使用以下命令进行测试：

PhotoApp.toggleVirtualScroll()         - 切换虚拟滚动模式
PhotoApp.getState()                    - 获取应用状态
PhotoApp.forceVirtualScroll()          - 强制启用虚拟滚动
PhotoApp.forceTraditional()            - 强制使用传统模式
PhotoApp.generateTestImages(500)       - 生成指定数量的测试图片
PhotoApp.testPreviewPerformance()      - 测试动态预览性能
PhotoApp.testMassiveImages(300)        - 🔥测试大量图片性能（解决崩溃问题）
PhotoApp.testScrollbarDragging(500)    - 🎯测试滚动条拖拽性能（解决白屏问题）
PhotoApp.monitorScrollPerformance()    - 📊监控滚动性能统计

🚀 最新优化功能:
✅ 虚拟滚动: ${appState.useVirtualScroll ? '开启' : '关闭'} (>10张图片自动启用)
✅ 智能滚动检测: 区分普通滚动和快速拖拽
✅ 动态缓冲区: 根据滚动速度自动调整缓冲区大小
✅ 请求取消机制: 自动取消未完成的图片加载请求
✅ 批量DOM操作: 减少重绘提升性能
✅ 滚动条拖拽优化: 解决快速拖拽时的白屏问题
✅ Windows风格平滑过渡: 模仿Windows照片查看器的温和切换效果

🎯 滚动性能优化:
- 慢速滚动: 16ms节流 + 3行缓冲区 = 流畅响应
- 快速滚动: 50ms防抖 + 6行缓冲区 = 减少白屏
- 滚动条拖拽: 检测拖拽状态 + 动态渲染 = 即时响应
- 请求管理: AbortController取消旧请求 = 避免资源浪费

💡 性能对比说明:
- 优化前: 快速拖拽滚动条 → 加载所有图片 → 白屏/卡顿/崩溃
- 优化后: 智能检测拖拽 → 只加载当前屏+缓冲区 → 流畅响应

🎨 图片预览过渡效果优化:
- 优化前: 弹出缩放动画 → 用户眼睛疲劳
- 优化后: Windows风格平滑淡入淡出 → 温和舒适的切换体验

🧪 测试建议:
1. 运行 PhotoApp.testScrollbarDragging(500) 测试滚动条拖拽
2. 快速拖动滚动条到不同位置，观察加载情况
3. 运行 PhotoApp.monitorScrollPerformance() 查看性能数据
`);

// 创建优化的图片URL（用于加速大图片加载）
function createOptimizedImageUrl(originalSrc, maxSize = 1200) {
    // 检查输入参数
    if (!originalSrc || typeof originalSrc !== 'string') {
        console.warn('createOptimizedImageUrl: 无效的图片路径', originalSrc);
        return '';
    }

    // 对于本地文件，我们返回原始路径但可以后续通过CSS限制显示尺寸
    if (originalSrc.startsWith('file://')) {
        return originalSrc;
    }

    // 对于Windows路径，确保正确处理
    if (originalSrc.match(/^[A-Za-z]:\\/)) {
        return `file://${originalSrc.replace(/\\/g, '/')}`;
    }

    // 对于外部URL（如picsum），可以指定尺寸参数实现真正的尺寸优化
    if (originalSrc.includes('picsum.photos')) {
        // 根据maxSize创建合适的图片URL
        const width = maxSize;
        const height = Math.round(maxSize * 0.75); // 4:3比例
        return originalSrc.replace(/(\d+)\/(\d+)/, `${width}/${height}`);
    }

    // 对于其他URL，直接返回
    return originalSrc;
}

// 显示图片放大预览（用于产品图片和广告图片）
async function showImageLightbox(imgElement) {
    try {
        // 获取原始图片路径
        let originalSrc = imgElement.dataset.imagePath || imgElement.src;
        const imageName = imgElement.alt || imgElement.title || '图片预览';

        console.log('showImageLightbox 调用:');
        console.log('  原始图片源:', originalSrc);
        console.log('  图片名称:', imageName);

        // 如果是file://协议，先移除它，确保传递的是纯路径
        if (originalSrc.startsWith('file://')) {
            originalSrc = originalSrc.substring(7);
            console.log('  移除file://前缀后:', originalSrc);
        }

        if (ipcRenderer) {
            // Electron环境：打开新窗口
            console.log('  使用Electron模式，调用主进程...');
            await ipcRenderer.invoke('open-image-preview', {
                imagePath: originalSrc,
                imageName: imageName
            });
            console.log('  主进程调用完成');
        } else {
            // 浏览器环境：在新标签页中打开
            console.log('  使用浏览器模式...');
            const previewUrl = `image-preview.html?imagePath=${encodeURIComponent(originalSrc)}&imageName=${encodeURIComponent(imageName)}`;
            console.log('  预览URL:', previewUrl);
            window.open(previewUrl, '_blank', 'width=1200,height=800,resizable=yes,scrollbars=no,toolbar=no,menubar=no');
        }

    } catch (error) {
        console.error('显示图片预览失败:', error);
        showToast('图片预览失败: ' + error.message, 'error');
    }
}

// 全局函数，供HTML调用
window.showImageLightbox = showImageLightbox;

// 调试函数：检查相框数据状态
function debugFrameData() {
    console.log('=== 相框数据调试信息 ===');
    console.log('相框类型数据:', appState.frameCategories);
    console.log('相框数据:', appState.availableFrames);

    // 检查数据匹配情况
    if (appState.frameCategories && appState.availableFrames) {
        console.log('数据匹配检查:');
        appState.availableFrames.forEach(frame => {
            const category = appState.frameCategories.find(cat => cat.id === frame.category_id);
            console.log(`相框 "${frame.name}" (ID: ${frame.id}):`, {
                category_id: frame.category_id,
                category_id_type: typeof frame.category_id,
                found_category: category ? category.name : '未找到',
                all_category_ids: appState.frameCategories.map(cat => ({ id: cat.id, type: typeof cat.id, name: cat.name }))
            });
        });
    }

    // 检查右键菜单状态
    const frameMenuOptions = document.getElementById('frame-menu-options');
    if (frameMenuOptions) {
        const menuItems = frameMenuOptions.querySelectorAll('.context-menu-item.has-submenu');
        console.log('右键菜单项数量:', menuItems.length);
        menuItems.forEach((item, index) => {
            const submenu = item.querySelector('.context-submenu');
            const submenuItems = submenu ? submenu.querySelectorAll('.context-menu-item') : [];
            console.log(`菜单项 ${index + 1}: "${item.textContent}" - 子菜单项数量: ${submenuItems.length}`);
        });
    }

    return {
        frameCategories: appState.frameCategories,
        frames: appState.availableFrames,
        menuInitialized: !!document.getElementById('frame-menu-options')
    };
}

// 强制重新加载相框数据
async function reloadFrameData() {
    console.log('强制重新加载相框数据...');
    try {
        if (ipcRenderer) {
            // 重新加载相框类型
            const frameCategories = await ipcRenderer.invoke('get-frame-categories');
            appState.frameCategories = frameCategories;
            console.log('重新加载的相框类型:', frameCategories);

            // 重新加载相框
            const frames = await ipcRenderer.invoke('get-frames');
            console.log('重新加载的相框:', frames);

            // 重新渲染相框菜单
            renderFrames(frames);
            console.log('相框菜单重新渲染完成');

            // 显示调试信息
            debugFrameData();
        } else {
            console.log('浏览器环境，无法重新加载数据');
        }
    } catch (error) {
        console.error('重新加载相框数据失败:', error);
    }
}

// 测试右键菜单功能
function testFrameContextMenu() {
    console.log('=== 测试右键菜单功能 ===');

    // 检查菜单元素是否存在
    const contextMenu = document.getElementById('frame-context-menu');
    const menuOptions = document.getElementById('frame-menu-options');

    if (!contextMenu || !menuOptions) {
        console.error('右键菜单元素未找到');
        return;
    }

    console.log('右键菜单元素存在');

    // 模拟显示右键菜单
    contextMenu.style.display = 'block';
    contextMenu.style.left = '100px';
    contextMenu.style.top = '100px';

    console.log('右键菜单已显示在屏幕上，请检查菜单项');

    // 检查菜单项
    const menuItems = menuOptions.querySelectorAll('.context-menu-item.has-submenu');
    console.log(`找到 ${menuItems.length} 个带子菜单的菜单项:`);

    menuItems.forEach((item, index) => {
        const submenu = item.querySelector('.context-submenu');
        const submenuItems = submenu ? submenu.querySelectorAll('.context-menu-item') : [];

        // 获取菜单项的直接文本内容（不包括子菜单）
        const categoryText = item.querySelector('span') ? item.querySelector('span').textContent :
                            Array.from(item.childNodes)
                                .filter(node => node.nodeType === Node.TEXT_NODE)
                                .map(node => node.textContent.trim())
                                .join('').trim();

        console.log(`  ${index + 1}. "${categoryText}" - 子菜单项: ${submenuItems.length} 个`);

        // 添加测试点击事件
        item.style.backgroundColor = '#e3f2fd';
        item.title = `点击测试 - ${categoryText}`;
    });

    // 10秒后自动隐藏
    setTimeout(() => {
        contextMenu.style.display = 'none';
        console.log('测试结束，右键菜单已隐藏');

        // 重置样式
        menuItems.forEach(item => {
            item.style.backgroundColor = '';
            item.title = '';
        });
    }, 10000);

    console.log('菜单将在10秒后自动隐藏，请在此期间测试菜单项的点击和悬停功能');
}

// 强制显示指定菜单项的子菜单
function forceShowSubmenu(categoryName) {
    const menuOptions = document.getElementById('frame-menu-options');
    if (!menuOptions) {
        console.error('菜单选项元素未找到');
        return;
    }

    const menuItems = menuOptions.querySelectorAll('.context-menu-item.has-submenu');
    let targetItem = null;

    menuItems.forEach(item => {
        // 获取菜单项的实际文本（不包括子菜单内容）
        const categoryText = item.querySelector('span') ? item.querySelector('span').textContent :
                            Array.from(item.childNodes)
                                .filter(node => node.nodeType === Node.TEXT_NODE)
                                .map(node => node.textContent.trim())
                                .join('').trim();

        if (categoryText.toLowerCase().includes(categoryName.toLowerCase())) {
            targetItem = item;
        }
    });

    if (!targetItem) {
        console.error(`未找到包含 "${categoryName}" 的菜单项`);
        console.log('可用的菜单项:', Array.from(menuItems).map(item => {
            const categoryText = item.querySelector('span') ? item.querySelector('span').textContent :
                                Array.from(item.childNodes)
                                    .filter(node => node.nodeType === Node.TEXT_NODE)
                                    .map(node => node.textContent.trim())
                                    .join('').trim();
            return categoryText;
        }));
        return;
    }

    const submenu = targetItem.querySelector('.context-submenu');
    if (!submenu) {
        console.error('未找到子菜单');
        return;
    }

    // 显示主菜单
    const contextMenu = document.getElementById('frame-context-menu');
    contextMenu.style.display = 'block';
    contextMenu.style.left = '100px';
    contextMenu.style.top = '100px';

    // 强制显示子菜单
    submenu.style.display = 'block';

    // 获取正确的菜单项名称
    const categoryText = targetItem.querySelector('span') ? targetItem.querySelector('span').textContent :
                        Array.from(targetItem.childNodes)
                            .filter(node => node.nodeType === Node.TEXT_NODE)
                            .map(node => node.textContent.trim())
                            .join('').trim();

    console.log(`已强制显示 "${categoryText}" 的子菜单`);

    // 高亮显示
    targetItem.style.backgroundColor = '#e3f2fd';
    submenu.style.border = '2px solid #007bff';

    // 10秒后隐藏
    setTimeout(() => {
        contextMenu.style.display = 'none';
        submenu.style.display = 'none';
        targetItem.style.backgroundColor = '';
        submenu.style.border = '';
        console.log('测试结束');
    }, 10000);
}

// 检查菜单DOM结构
function inspectMenuDOMStructure() {
    console.log('=== 检查菜单DOM结构 ===');

    const menuOptions = document.getElementById('frame-menu-options');
    if (!menuOptions) {
        console.error('菜单选项元素未找到');
        return;
    }

    console.log('菜单容器HTML:');
    console.log(menuOptions.outerHTML);

    const menuItems = menuOptions.querySelectorAll('.context-menu-item.has-submenu');
    console.log(`\n找到 ${menuItems.length} 个带子菜单的菜单项:`);

    menuItems.forEach((item, index) => {
        console.log(`\n--- 菜单项 ${index + 1} ---`);
        console.log('HTML结构:', item.outerHTML);
        console.log('直接文本内容:', item.textContent);
        console.log('子节点数量:', item.childNodes.length);

        // 检查直接的文本节点
        const directTextNodes = Array.from(item.childNodes).filter(node => node.nodeType === Node.TEXT_NODE);
        console.log('直接文本节点:', directTextNodes.map(node => `"${node.textContent.trim()}"`));

        const submenu = item.querySelector('.context-submenu');
        if (submenu) {
            const submenuItems = submenu.querySelectorAll('.context-menu-item');
            console.log(`子菜单项数量: ${submenuItems.length}`);
            submenuItems.forEach((subItem, subIndex) => {
                console.log(`  ${subIndex + 1}. "${subItem.textContent}"`);
            });
        }
    });
}

// 清理并重新生成菜单
function cleanAndRegenerateMenu() {
    console.log('=== 清理并重新生成菜单 ===');

    // 先检查当前数据
    console.log('当前相框类型数据:', appState.frameCategories);
    console.log('当前相框数据:', appState.availableFrames);

    if (!appState.frameCategories || !appState.availableFrames) {
        console.log('数据未加载，先重新加载数据...');
        return reloadFrameData().then(() => {
            console.log('数据重新加载完成，现在重新生成菜单...');
            renderFrames(appState.availableFrames);
            inspectMenuDOMStructure();
        });
    }

    // 清理现有菜单
    const menuOptions = document.getElementById('frame-menu-options');
    if (menuOptions) {
        menuOptions.innerHTML = '';
        console.log('已清理现有菜单');
    }

    // 重新生成菜单
    renderFrames(appState.availableFrames);
    console.log('菜单重新生成完成');

    // 检查结果
    setTimeout(() => {
        inspectMenuDOMStructure();
    }, 100);
}

// 强制测试右键菜单 - 修复版
function testFrameContextMenuFixed() {
    console.log('=== 测试右键菜单功能（修复版） ===');

    // 检查菜单元素是否存在
    const contextMenu = document.getElementById('frame-context-menu');
    const menuOptions = document.getElementById('frame-menu-options');

    if (!contextMenu || !menuOptions) {
        console.error('右键菜单元素未找到，开始重新初始化...');

        // 强制重新加载数据
        return loadInitialData().then(() => {
            setTimeout(() => {
                testFrameContextMenuFixed();
            }, 1000);
        });
    }

    console.log('✅ 右键菜单元素存在');

    // 检查菜单内容
    const menuItems = menuOptions.querySelectorAll('.context-menu-item');
    console.log(`菜单项总数: ${menuItems.length}`);

    if (menuItems.length === 0) {
        console.log('菜单项为空，重新渲染菜单...');

        // 重新渲染菜单
        if (appState.availableFrames && appState.availableFrames.length > 0) {
            renderFrames(appState.availableFrames);
        } else {
            console.log('相框数据为空，重新加载数据...');
            return reloadFrameData().then(() => {
                setTimeout(() => {
                    testFrameContextMenuFixed();
                }, 500);
            });
        }

        setTimeout(() => {
            testFrameContextMenuFixed();
        }, 500);
        return;
    }

    // 模拟选择一张图片
    if (appState.selectedImages.length === 0 && appState.originalImages.length > 0) {
        console.log('添加一张测试图片到精修区...');
        addToSelected(appState.originalImages[0]);
        appState.selectedImageForFrame = appState.originalImages[0];
    }

    // 显示右键菜单
    console.log('显示右键菜单...');
    showFrameContextMenu(200, 200);

    console.log('=== 菜单项详情 ===');
    menuItems.forEach((item, index) => {
        const isHasSubmenu = item.classList.contains('has-submenu');
        const submenu = item.querySelector('.context-submenu');
        const submenuItems = submenu ? submenu.querySelectorAll('.context-menu-item') : [];

        console.log(`菜单项 ${index + 1}:`, {
            文本: item.textContent.trim(),
            类名: item.className,
            有子菜单: isHasSubmenu,
            子菜单项数: submenuItems.length,
            子菜单显示: submenu ? submenu.style.display : 'none'
        });

        if (isHasSubmenu) {
            // 为带子菜单的项目添加测试样式
            item.style.backgroundColor = '#e3f2fd';
            item.title = `测试菜单项 - ${item.textContent.trim()}`;
        }
    });

    console.log('✅ 右键菜单测试完成');
    console.log('💡 请手动测试：');
    console.log('1. 鼠标悬停在蓝色高亮的菜单项上');
    console.log('2. 观察子菜单是否正确显示');
    console.log('3. 点击子菜单项测试功能');

    // 30秒后自动清理
    setTimeout(() => {
        hideFrameContextMenu();
        // 清理测试样式
        menuItems.forEach(item => {
            item.style.backgroundColor = '';
            item.title = '';
        });
        console.log('测试结束，菜单已隐藏');
    }, 30000);
}

// 添加到全局调试函数
window.debugFrameData = debugFrameData;
window.reloadFrameData = reloadFrameData;
window.testFrameContextMenu = testFrameContextMenu;
window.testFrameContextMenuFixed = testFrameContextMenuFixed;
window.forceShowSubmenu = forceShowSubmenu;
window.inspectMenuDOMStructure = inspectMenuDOMStructure;
window.cleanAndRegenerateMenu = cleanAndRegenerateMenu;

// 预加载图片并返回尺寸信息
async function preloadImageWithSize(src) {
    return new Promise((resolve) => {
        const img = new Image();
        img.crossOrigin = 'anonymous';

        // 超时处理
        const timeout = setTimeout(() => {
            console.warn('图片预加载超时:', src);
            resolve({
                src: src,
                width: 1920,
                height: 1080,
                loaded: false
            });
        }, 3000); // 减少超时时间到3秒

        img.onload = function() {
            clearTimeout(timeout);
            resolve({
                src: src,
                width: this.naturalWidth || this.width,
                height: this.naturalHeight || this.height,
                loaded: true
            });
        };

        img.onerror = function() {
            clearTimeout(timeout);
            console.warn('图片预加载失败:', src);
            resolve({
                src: src,
                width: 1920,
                height: 1080,
                loaded: false
            });
        };

        // 使用优化的URL
        img.src = createOptimizedImageUrl(src);
    });
}

// 调试函数：检查画廊状态
function debugGalleryState() {
    console.log('=== 画廊调试信息 ===');
    console.log('原始图片数量:', appState.originalImages.length);
    console.log('选中图片数量:', appState.selectedImages.length);
    console.log('使用虚拟滚动:', appState.useVirtualScroll);
    console.log('虚拟滚动实例:', !!appState.originalGalleryVirtualScroll);

    const galleryContainer = document.getElementById('original-gallery');
    const thumbnails = galleryContainer.querySelectorAll('.image-thumbnail, .virtual-image-thumbnail');
    console.log('DOM中的缩略图数量:', thumbnails.length);

    thumbnails.forEach((thumb, index) => {
        const imageId = thumb.dataset.imageId;
        const hasClickListener = thumb.onclick !== null || thumb.addEventListener !== null;
        console.log(`缩略图 ${index + 1}:`, {
            imageId,
            visible: thumb.offsetWidth > 0 && thumb.offsetHeight > 0,
            clickable: thumb.style.pointerEvents !== 'none',
            hasClickListener
        });
    });

    // 检查点击事件
    console.log('点击第一个缩略图进行测试...');
    if (thumbnails.length > 0) {
        const firstThumbnail = thumbnails[0];
        const rect = firstThumbnail.getBoundingClientRect();
        console.log('第一个缩略图位置和尺寸:', rect);
    }
}

// 添加到全局调试函数
window.debugGallery = debugGalleryState;

// 测试所有缩略图点击功能
function testAllThumbnailClicks() {
    console.log('=== 测试所有缩略图点击功能 ===');
    const galleryContainer = document.getElementById('original-gallery');
    const thumbnails = galleryContainer.querySelectorAll('.image-thumbnail, .virtual-image-thumbnail');

    if (thumbnails.length === 0) {
        console.log('未找到缩略图，请先导入图片');
        return;
    }

    thumbnails.forEach((thumb, index) => {
        const imageId = thumb.dataset.imageId;
        const rect = thumb.getBoundingClientRect();
        const isVisible = rect.width > 0 && rect.height > 0;
        const isClickable = thumb.style.pointerEvents !== 'none';

        console.log(`缩略图 ${index + 1}:`, {
            imageId,
            位置: `${Math.round(rect.left)}, ${Math.round(rect.top)}`,
            尺寸: `${Math.round(rect.width)} x ${Math.round(rect.height)}`,
            可见: isVisible,
            可点击: isClickable
        });

        // 为每个缩略图添加测试标记
        if (isVisible && isClickable) {
            thumb.style.border = '2px solid green';
            setTimeout(() => {
                thumb.style.border = '';
            }, 2000);
        } else {
            thumb.style.border = '2px solid red';
            setTimeout(() => {
                thumb.style.border = '';
            }, 2000);
        }
    });

    // 模拟点击第二个缩略图进行测试
    if (thumbnails.length > 1) {
        console.log('模拟点击第二个缩略图...');
        setTimeout(() => {
            const secondThumbnail = thumbnails[1];
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            secondThumbnail.dispatchEvent(clickEvent);
        }, 1000);
    }
}

// 添加到全局
window.testThumbnails = testAllThumbnailClicks;

// 测试Viewer.js预览功能
function testViewerPreview() {
    console.log('=== 测试Viewer.js预览功能 ===');

    if (appState.originalImages.length === 0) {
        console.log('请先导入图片');
        return;
    }

    console.log('开始测试预览...');

    // 测试预览第二张图片（如果存在）
    const testIndex = Math.min(1, appState.originalImages.length - 1);
    const testImage = appState.originalImages[testIndex];

    console.log(`测试预览第${testIndex + 1}张图片:`, testImage.name);

    // 直接调用showLightbox函数
    showLightbox(testImage, 'original').then(() => {
        console.log('✅ 预览测试成功 - Viewer.js已正常工作');
    }).catch(error => {
        console.error('❌ 预览测试失败:', error);
    });
}

// 添加到全局
window.testViewer = testViewerPreview;

// 简单的性能监控
function getPreviewStats() {
    const galleryContainer = document.getElementById('original-gallery');
    const thumbnails = galleryContainer.querySelectorAll('.image-thumbnail, .virtual-image-thumbnail');

    return {
        缩略图数量: thumbnails.length,
        原始图片数量: appState.originalImages.length,
        已选中数量: appState.selectedImages.length,
        内存使用提示: '使用懒加载和尺寸优化提升性能'
    };
}

// 添加到全局
window.getPreviewStats = getPreviewStats;

// 获取图片拍摄时间
async function getImageShootingTime(imagePath) {
    if (!ipcRenderer) return null;

    try {
        const exifData = await ipcRenderer.invoke('get-image-exif', imagePath);
        if (exifData && exifData.shootingTime) {
            // 格式化时间显示 - 修复日期解析问题
            let dateStr = exifData.shootingTime;

            // EXIF时间格式通常是 "YYYY:MM:DD HH:MM:SS"
            // 需要转换为标准格式
            if (dateStr.includes(':')) {
                // 替换前两个冒号为短横线，保留时间部分的冒号
                const parts = dateStr.split(' ');
                if (parts.length >= 2) {
                    const datePart = parts[0].replace(/:/g, '-');
                    const timePart = parts[1];
                    dateStr = `${datePart} ${timePart}`;
                }
            }

            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }
        }
    } catch (error) {
        console.error('获取图片拍摄时间失败:', error);
    }

    return null;
}

// 截图并保存合同
async function captureAndSaveContract(element, customerName) {
    if (!ipcRenderer) return;

    try {
        console.log('开始截图，元素:', element);

        // 等待一下确保DOM完全渲染
        await new Promise(resolve => setTimeout(resolve, 500));

        // 使用html2canvas进行截图
        const canvas = await html2canvas(element, {
            backgroundColor: '#ffffff',
            scale: 1,
            useCORS: true,
            allowTaint: true,
            logging: true,
            width: element.offsetWidth,
            height: element.offsetHeight,
            scrollX: 0,
            scrollY: 0
        });

        console.log('html2canvas截图成功，canvas尺寸:', canvas.width, 'x', canvas.height);

        const imageData = canvas.toDataURL('image/png');

        // 生成文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `合同截图_${customerName}_${timestamp}.png`;

        // 保存到精修文件夹
        if (appState.sourceFolderPath) {
            const packagePrice = appState.currentPackage.price;
            const folderName = `${customerName}十${packagePrice}`;
            const folderPath = appState.sourceFolderPath + '\\' + folderName;

            const result = await ipcRenderer.invoke('save-screenshot', {
                imageData: imageData,
                folderPath: folderPath,
                filename: filename
            });

            if (result.success) {
                console.log('合同截图保存成功:', result.path);
                showToast('合同截图已保存到精修文件夹');
            } else {
                console.error('保存截图失败:', result.error);
                showToast('截图保存失败', 'error');
            }
        }
    } catch (error) {
        console.error('html2canvas截图失败:', error);
        showToast('截图失败: ' + error.message, 'error');
    }
}

// 备用截图方案
async function captureContractFallback(element, customerName) {
    try {
        // 使用Canvas API进行简单截图
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = 800;
        canvas.height = 800; // 增加高度以容纳签名区域

        // 设置白色背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 绘制表单内容
        ctx.font = '16px Arial, sans-serif';
        ctx.fillStyle = '#000000';

        // 获取表单数据
        const customerNameInput = element.querySelector('#customer-name-input');
        const additionalRefineInput = element.querySelector('#additional-refine-input');
        const notesInput = element.querySelector('#notes-input');
        const renewPackage = element.querySelector('input[name="renew-package"]:checked')?.value === 'yes' ? '是' : '否';
        const allowDisplay = element.querySelector('input[name="allow-display"]:checked')?.value === 'yes' ? '是' : '否';
        const shootingTime = element.querySelector('#shooting-time-display')?.textContent || '';

        let y = 40;
        const lineHeight = 30;

        // 绘制标题
        ctx.font = 'bold 20px Arial, sans-serif';
        ctx.fillText('客户合同信息', 20, y);
        y += 40;

        ctx.font = '16px Arial, sans-serif';
        ctx.fillText(`姓名: ${customerNameInput.value}`, 20, y); y += lineHeight;
        ctx.fillText(`套餐: ${appState.currentPackage.name}`, 20, y); y += lineHeight;
        ctx.fillText(`精修张数: ${appState.currentPackage.count}张`, 20, y); y += lineHeight;
        ctx.fillText(`拍摄日期: ${shootingTime}`, 20, y); y += lineHeight;
        ctx.fillText(`增加精修: ${additionalRefineInput.value}张`, 20, y); y += lineHeight;
        ctx.fillText(`是否续订套餐: ${renewPackage}`, 20, y); y += lineHeight;
        ctx.fillText(`照片可否让我们展示: ${allowDisplay}`, 20, y); y += lineHeight;
        y += 20;
        ctx.fillText(`备注:`, 20, y); y += lineHeight;

        // 处理多行备注
        const notes = notesInput.value;
        if (notes) {
            const maxWidth = canvas.width - 40;
            const words = notes.split('');
            let line = '';
            for (let i = 0; i < words.length; i++) {
                const testLine = line + words[i];
                const metrics = ctx.measureText(testLine);
                if (metrics.width > maxWidth && i > 0) {
                    ctx.fillText(line, 40, y);
                    line = words[i];
                    y += lineHeight;
                } else {
                    line = testLine;
                }
            }
            if (line) {
                ctx.fillText(line, 40, y);
                y += lineHeight;
            }
        }

        // 添加签名
        y += 20;
        ctx.fillText('客户签名:', 20, y);
        y += 10;

        // 获取签名canvas并绘制到截图中
        const signatureCanvas = element.querySelector('#signature-canvas');
        if (signatureCanvas) {
            // 获取签名canvas的实际显示尺寸
            const canvasRect = signatureCanvas.getBoundingClientRect();
            const signatureWidth = Math.min(canvasRect.width, 600); // 限制最大宽度
            const signatureHeight = Math.min(canvasRect.height, 200); // 限制最大高度

            // 绘制签名边框
            ctx.strokeStyle = '#ccc';
            ctx.lineWidth = 1;
            ctx.strokeRect(20, y, signatureWidth, signatureHeight);

            // 将签名canvas内容绘制到截图canvas中，保持比例
            ctx.drawImage(signatureCanvas, 20, y, signatureWidth, signatureHeight);
        }

        const imageData = canvas.toDataURL('image/png');

        // 生成文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `备注文件_${customerName}_${timestamp}.png`;

        // 保存到精修文件夹
        if (appState.sourceFolderPath) {
            const packagePrice = appState.currentPackage.price;
            const folderName = `${customerName}十${packagePrice}`;
            const folderPath = appState.sourceFolderPath + '\\' + folderName;

            const result = await ipcRenderer.invoke('save-screenshot', {
                imageData: imageData,
                folderPath: folderPath,
                filename: filename
            });

            if (result.success) {
                console.log('合同截图保存成功:', result.path);
                showToast('合同截图已保存到精修文件夹');
            }
        }
    } catch (error) {
        console.error('备用截图方案也失败:', error);
        showToast('截图保存失败', 'error');
    }
}

// 加载公告和服务图片
async function loadAnnouncementImages(container) {
    const images = container.querySelectorAll('img[data-image-path]');

    for (const img of images) {
        const imagePath = img.dataset.imagePath;
        if (!imagePath) continue;

        try {
            let imgSrc = imagePath;

            // 如果在Electron环境且是相对路径，获取完整路径
            if (ipcRenderer && !imagePath.startsWith('http') && !imagePath.startsWith('data:')) {
                const fullPath = await ipcRenderer.invoke('get-image-path', imagePath);
                if (fullPath) {
                    imgSrc = `file://${fullPath}`;
                }
            }

            // 创建图片对象进行预加载
            const imageObj = new Image();
            imageObj.onload = () => {
                img.src = imgSrc;
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.3s ease';
                setTimeout(() => {
                    img.style.opacity = '1';
                }, 50);
            };

            imageObj.onerror = () => {
                console.warn('加载图片失败:', imagePath);
                // 隐藏无法加载的图片容器
                const imageContainer = img.closest('.announcement-image');
                if (imageContainer) {
                    imageContainer.style.display = 'none';
                }
            };

            imageObj.src = imgSrc;
        } catch (error) {
            console.error('处理图片路径失败:', error);
            // 隐藏出错的图片容器
            const imageContainer = img.closest('.announcement-image');
            if (imageContainer) {
                imageContainer.style.display = 'none';
            }
        }
    }
}

// 图片缩放处理函数
function scaleImageToFit(img, maxWidth = 200, maxHeight = 150) {
    const aspectRatio = img.naturalWidth / img.naturalHeight;
    let width = img.naturalWidth;
    let height = img.naturalHeight;

    // 如果图片尺寸超过最大尺寸，进行缩放
    if (width > maxWidth || height > maxHeight) {
        if (aspectRatio > maxWidth / maxHeight) {
            // 宽度是限制因素
            width = maxWidth;
            height = maxWidth / aspectRatio;
        } else {
            // 高度是限制因素
            height = maxHeight;
            width = maxHeight * aspectRatio;
        }
    }

    img.style.width = width + 'px';
    img.style.height = height + 'px';
}

// 加载套餐图片
async function loadPackageImages(container) {
    const images = container.querySelectorAll('img[data-image-path]');

    for (const img of images) {
        const imagePath = img.dataset.imagePath;
        if (!imagePath) continue;

        try {
            let imgSrc = imagePath;

            // 如果在Electron环境且是相对路径，获取完整路径
            if (ipcRenderer && !imagePath.startsWith('http') && !imagePath.startsWith('data:')) {
                const fullPath = await ipcRenderer.invoke('get-image-path', imagePath);
                if (fullPath) {
                    imgSrc = `file://${fullPath}`;
                }
            }

            // 创建图片对象进行预加载
            const imageObj = new Image();
            imageObj.onload = () => {
                img.src = imgSrc;
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.3s ease';
                // 对套餐图片进行尺寸控制
                scalePackageImageToFit(img);

                // 为套餐选择模态框中的图片添加点击放大功能
                if (container.closest('.package-modal')) {
                    img.style.cursor = 'pointer';
                    img.title = '点击查看大图';
                    img.onclick = function() { showImageLightbox(this); };
                }

                setTimeout(() => {
                    img.style.opacity = '1';
                }, 50);
            };

            imageObj.onerror = () => {
                console.warn('加载套餐图片失败:', imagePath);
                // 隐藏无法加载的图片
                img.style.display = 'none';
            };

            imageObj.src = imgSrc;
        } catch (error) {
            console.error('处理套餐图片路径失败:', error);
            // 隐藏出错的图片
            img.style.display = 'none';
        }
    }
}

// 套餐图片缩放处理函数
function scalePackageImageToFit(img) {
    const maxWidth = 180;  // 套餐卡片中图片的最大宽度
    const maxHeight = 120; // 套餐卡片中图片的最大高度

    img.onload = function() {
        const aspectRatio = this.naturalWidth / this.naturalHeight;
        let width = this.naturalWidth;
        let height = this.naturalHeight;

        // 如果图片尺寸超过最大尺寸，进行缩放
        if (width > maxWidth || height > maxHeight) {
            if (aspectRatio > maxWidth / maxHeight) {
                // 宽度是限制因素
                width = maxWidth;
                height = maxWidth / aspectRatio;
            } else {
                // 高度是限制因素
                height = maxHeight;
                width = maxHeight * aspectRatio;
            }
        }

        this.style.width = width + 'px';
        this.style.height = height + 'px';
        this.style.objectFit = 'cover';
    };
}

// ==================== 回收站功能 ====================

// 更新回收站计数
function updateRecycleBinCount() {
    const count = appState.recycledImages.length;
    elements.recycleCount.textContent = count;
    elements.recycleBinBtn.style.opacity = count > 0 ? '1' : '0.6';
}

// 显示回收站模态框
function showRecycleBinModal() {
    elements.recycleBinModal.style.display = 'flex';
    renderRecycledGallery();
}

// 隐藏回收站模态框
function hideRecycleBinModal() {
    elements.recycleBinModal.style.display = 'none';
}

// 渲染回收站图片列表
function renderRecycledGallery() {
    if (appState.recycledImages.length === 0) {
        elements.recycledGallery.innerHTML = '<div class="empty-state"><p>回收站为空</p></div>';
        return;
    }

    elements.recycledGallery.innerHTML = '';

    appState.recycledImages.forEach((image, index) => {
        const thumbnail = createRecycledImageThumbnail(image, index);
        elements.recycledGallery.appendChild(thumbnail);
    });
}

// 创建回收站图片缩略图（使用与精选区相同的结构）
function createRecycledImageThumbnail(image, index) {
    const div = document.createElement('div');
    div.className = 'image-thumbnail recycled-thumbnail';
    div.dataset.imageId = image.id;
    div.dataset.type = 'recycled';

    const img = document.createElement('img');
    // 使用与精选区相同的懒加载和占位符
    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjhmOWZhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZjNzU3ZCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWKoOi9veS4rS4uLjwvdGV4dD48L3N2Zz4=';
    img.dataset.src = ipcRenderer ? `file://${image.path}` : image.path;
    img.alt = image.name;
    img.loading = 'lazy';

    // 实现与精选区相同的懒加载逻辑
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                // 对于模拟图片，直接加载；对于本地文件，使用优化
                if (img.dataset.src.startsWith('https://picsum.photos')) {
                    // 直接加载外部图片
                    img.src = img.dataset.src;
                    img.onload = () => {
                        img.style.opacity = '1';
                    };
                    img.onerror = () => {
                        img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1IiBzdHJva2U9IiNkZGQiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5Yqg6L295aSx6LSlPC90ZXh0Pjwvc3ZnPg==';
                        img.style.opacity = '1';
                    };
                } else {
                    // 对于本地文件使用优化加载
                    loadImageWithOptimization(img, img.dataset.src);
                }
                observer.unobserve(img);
            }
        });
    }, { threshold: 0.1, rootMargin: '50px' }); // 增加rootMargin提前加载

    observer.observe(img);
    img.style.opacity = '0.5';
    img.style.transition = 'opacity 0.3s ease';

    // 使用与精选区相同的图片信息结构
    const info = document.createElement('div');
    info.className = 'image-info';
    const deletedTime = new Date(image.deletedAt).toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
    info.innerHTML = `
        <div class="deleted-time" style="font-size: 10px; color: #ffc107; margin-bottom: 2px;">删除: ${deletedTime}</div>
        ${image.frame ? `<div class="frame-info">相框: ${image.frame}</div>` : ''}
    `;

    // 添加预览提示图标（与精选区相同）
    const previewHint = document.createElement('div');
    previewHint.className = 'preview-hint';
    previewHint.innerHTML = '👁️';
    previewHint.title = '点击恢复到精修区';

    // 恢复按钮（替代原来的按钮，使用更小的样式）
    const restoreBtn = document.createElement('div');
    restoreBtn.className = 'restore-indicator';
    restoreBtn.innerHTML = '↩️';
    restoreBtn.title = '点击恢复到精修区';
    restoreBtn.style.cssText = `
        position: absolute;
        top: 8px;
        right: 8px;
        background: rgba(40, 167, 69, 0.9);
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        z-index: 3;
        transition: all 0.2s ease;
    `;

    div.appendChild(img);
    div.appendChild(info);
    div.appendChild(previewHint);
    div.appendChild(restoreBtn);

    // 单击恢复图片
    div.addEventListener('click', (e) => {
        e.stopPropagation();
        restoreImageFromRecycleBin(image);
    });

    // 右键永久删除
    div.addEventListener('contextmenu', (e) => {
        e.preventDefault();
        showPermanentDeleteConfirmation(image);
    });

    return div;
}

// 从回收站恢复图片
function restoreImageFromRecycleBin(image) {
    // 检查是否已经在精修区中
    if (appState.selectedImages.find(img => img.id === image.id)) {
        showToast('该图片已在精修区中', 'warning');
        return;
    }

    // 从回收站移除
    appState.recycledImages = appState.recycledImages.filter(img => img.id !== image.id);

    // 添加到精修区（移除删除时间等回收站属性）
    const restoredImage = {
        id: image.id,
        name: image.name,
        path: image.path,
        frame: image.frame || null,
        selectedAt: new Date().toISOString()
    };

    appState.selectedImages.push(restoredImage);

    // 更新界面
    if (appState.originalGalleryVirtualScroll) {
        appState.originalGalleryVirtualScroll.updateItemState(image.id, {
            inSelected: true
        });
        renderSelectedGallery();
    } else {
        renderSelectedGallery();
        renderOriginalGallery();
    }

    updateUI();
    updateRecycleBinCount();
    renderRecycledGallery();

    showToast(`已恢复 ${image.name} 到精修区`);
}

// 显示永久删除确认对话框
function showPermanentDeleteConfirmation(image) {
    showConfirmDialog(
        '永久删除确认',
        `确定要永久删除 "${image.name}" 吗？此操作不可撤销。`,
        () => permanentDeleteImage(image)
    );
}

// 永久删除图片
function permanentDeleteImage(image) {
    appState.recycledImages = appState.recycledImages.filter(img => img.id !== image.id);
    updateRecycleBinCount();
    renderRecycledGallery();
    showToast(`已永久删除 ${image.name}`);
}

// 清空回收站
function emptyRecycleBin() {
    if (appState.recycledImages.length === 0) {
        showToast('回收站已为空', 'info');
        return;
    }

    showConfirmDialog(
        '清空回收站',
        `确定要清空回收站吗？这将永久删除 ${appState.recycledImages.length} 张照片，此操作不可撤销。`,
        () => {
            appState.recycledImages = [];
            updateRecycleBinCount();
            renderRecycledGallery();
            showToast('回收站已清空');
        }
    );
}

// 显示冲突处理对话框
function showConflictDialog(image, recycledIndex) {
    const recycledImage = appState.recycledImages[recycledIndex];

    elements.conflictMessage.textContent = `照片 "${image.name}" 已在回收站中，您希望如何处理？`;
    elements.conflictImageName.textContent = image.name;

    // 设置冲突图片预览
    if (image.path.startsWith('https://picsum.photos')) {
        elements.conflictImage.src = image.path;
    } else {
        elements.conflictImage.src = ipcRenderer ? `file://${image.path}` : image.path;
    }

    // 保存当前处理的图片信息
    elements.conflictDialog.dataset.imageId = image.id;
    elements.conflictDialog.dataset.recycledIndex = recycledIndex;

    elements.conflictDialog.style.display = 'flex';
}

// 隐藏冲突处理对话框
function hideConflictDialog() {
    elements.conflictDialog.style.display = 'none';
    delete elements.conflictDialog.dataset.imageId;
    delete elements.conflictDialog.dataset.recycledIndex;
}

// 处理冲突覆盖
function handleConflictOverride() {
    const imageId = elements.conflictDialog.dataset.imageId;
    const recycledIndex = parseInt(elements.conflictDialog.dataset.recycledIndex);

    // 从原始图片中找到对应图片
    const originalImage = appState.originalImages.find(img => img.id === imageId);
    if (!originalImage) return;

    // 从回收站移除旧的图片
    appState.recycledImages.splice(recycledIndex, 1);

    // 添加到精修区
    appState.selectedImages.push({
        ...originalImage,
        frame: null,
        selectedAt: new Date().toISOString()
    });

    // 更新界面
    if (appState.originalGalleryVirtualScroll) {
        appState.originalGalleryVirtualScroll.updateItemState(originalImage.id, {
            inSelected: true
        });
        renderSelectedGallery();
    } else {
        renderSelectedGallery();
        renderOriginalGallery();
    }

    updateUI();
    updateRecycleBinCount();

    hideConflictDialog();
    showToast(`已覆盖并添加 ${originalImage.name} 到精修区`);
}

// ==================== 通用对话框功能 ====================

let confirmCallback = null;

// 显示确认对话框
function showConfirmDialog(title, message, callback) {
    elements.confirmTitle.textContent = title;
    elements.confirmMessage.textContent = message;
    confirmCallback = callback;
    elements.confirmDialog.style.display = 'flex';
}

// 隐藏确认对话框
function hideConfirmDialog() {
    elements.confirmDialog.style.display = 'none';
    confirmCallback = null;
}

// 确认操作
function confirmAction() {
    if (confirmCallback) {
        confirmCallback();
    }
    hideConfirmDialog();
}


