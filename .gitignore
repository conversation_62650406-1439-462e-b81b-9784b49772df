# Node dependencies
node_modules/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Build output
/dist/
/dist1/
/build/
/out/
/release/
*.asar

# Caches
.cache/
.eslintcache
.stylelintcache
.parcel-cache/
.vite/
.nyc_output/
coverage/

# Pack artifacts
*.tgz

# Environment variables
.env
.env.*
!.env.example

# IDE/editor
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.swp
*.swm
*.sw?

# OS files
.DS_Store
Thumbs.db
ehthumbs.db
desktop.ini
$RECYCLE.BIN/

# Electron builder / packaging
.electron/
.electron-builder/
.webpack/
release-builds/
*.dmg
*.exe
*.msi
*.blockmap
*.snap
*.AppImage
*.zip

# Temporary files
*.tmp
*.temp
*.bak

# Misc
*.orig
*.rej 